<?php
// <PERSON><PERSON><PERSON> to remove rule_id and input_id from all objects in the JSON file

// Read the JSON file
$jsonContent = file_get_contents('bre_from_staging.json');

if ($jsonContent === false) {
    die("Error: Could not read the JSON file.\n");
}

// Decode the JSON
$data = json_decode($jsonContent, true);

if ($data === null) {
    die("Error: Invalid JSON format.\n");
}

// Process each object to remove rule_id and input_id
foreach ($data as &$object) {
    // Remove rule_id if it exists
    if (isset($object['rule_id'])) {
        unset($object['rule_id']);
    }
    
    // Remove input_id if it exists
    if (isset($object['input_id'])) {
        unset($object['input_id']);
    }
}

// Encode back to JSON with pretty formatting
$newJsonContent = json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);

if ($newJsonContent === false) {
    die("Error: Could not encode JSON.\n");
}

// Write the modified content back to the file
$result = file_put_contents('bre_from_staging.json', $newJsonContent);

if ($result === false) {
    die("Error: Could not write to the JSON file.\n");
}

echo "Successfully removed rule_id and input_id from all objects in the JSON file.\n";
echo "Total objects processed: " . count($data) . "\n";
?>
