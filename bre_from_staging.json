[{"rule_id": 6109, "rule_name": "Compromised Parts - Direct to Recycle Processed", "rule_description": "Arrived at RRL with At Risk Parts from an AWS Incident", "priority": 1, "workflow_id": "10", "input_id": "131,132,133,134,138,135,136,139,140,141,149,137,142,130", "disposition_id": 27, "status": "Active", "rule_summary": "input = \"Fail-DamagedBodyChassis OR Fail-DamagedChipsPins OR Fail-DamagedCords OR Fail-DamagedFixtures OR Fail-DamagedOrMissingHeatSink OR Fail-DamagedTestAccessPoint OR Fail-ExcessiveRust OR Fail-FRUMissingCombination OR Fail-FRUMissingFan OR Fail-FRUMissingPSU OR Fail-ImproperPackaging OR Fail-MissingCriticalPart OR Fail-OutdatedHeatSinkOrPins OR Pass-Evaluation\" , , Facility = \"All\" \"", "created_date": "2025-06-05 18:56:09", "created_by": 733, "updated_date": null, "updated_by": null, "FacilityID": "all", "sub_disposition_id": null, "version_id": 71, "rule_id_text": "BRE_Rework_Prod_Copy_v5-6109", "AWSCustomerID": "all", "part_types": "All", "idCustomertype": "17", "MaterialType": "all"}, {"rule_id": 6110, "rule_name": "SidelineRecoveryProject", "rule_description": "A stakeholder has targeted this part for additional research, sideline for a recovery project", "priority": 2, "workflow_id": "10", "input_id": "130,131,132,133,134,135,136,137,138,139,140,141,142,149", "disposition_id": 49, "status": "Active", "rule_summary": "input = \"Pass-Evaluation OR Fail-DamagedBodyChassis OR Fail-DamagedChipsPins OR Fail-DamagedCords OR Fail-DamagedFixtures OR Fail-DamagedTestAccessPoint OR Fail-ExcessiveRust OR Fail-MissingCriticalPart OR Fail-DamagedOrMissingHeatSink OR Fail-FRUMissingCombination OR Fail-FRUMissingFan OR Fail-FRUMissingPSU OR Fail-OutdatedHeatSinkOrPins OR Fail-ImproperPackaging\" , Recovery Project = \"Yes\"  , Facility = \"All\" ", "created_date": "2025-06-05 18:56:09", "created_by": 733, "updated_date": null, "updated_by": null, "FacilityID": "all", "sub_disposition_id": null, "version_id": 71, "rule_id_text": "BRE_Rework_Prod_Copy_v5-6110", "AWSCustomerID": "all", "part_types": "All", "idCustomertype": "all", "MaterialType": "all"}, {"rule_id": 6111, "rule_name": "SidelineMBGraviton", "rule_description": "Must sideline all motherboards that have a graviton CPU. For extraction later", "priority": 3, "workflow_id": "10", "input_id": "130,131,132,133,134,135,136,137,138,139,140,141,142,149", "disposition_id": 96, "status": "Active", "rule_summary": "input = \"Pass-Evaluation OR Fail-DamagedBodyChassis OR Fail-DamagedChipsPins OR Fail-DamagedCords OR Fail-DamagedFixtures OR Fail-DamagedTestAccessPoint OR Fail-ExcessiveRust OR Fail-MissingCriticalPart OR Fail-DamagedOrMissingHeatSink OR Fail-FRUMissingCombination OR Fail-FRUMissingFan OR Fail-FRUMissingPSU OR Fail-OutdatedHeatSinkOrPins OR Fail-ImproperPackaging\" , Attribute ID Contains \"Extract-CPU\"  , Facility = \"All\" ", "created_date": "2025-06-05 18:56:09", "created_by": 733, "updated_date": null, "updated_by": null, "FacilityID": "all", "sub_disposition_id": null, "version_id": 71, "rule_id_text": "BRE_Rework_Prod_Copy_v5-6111", "AWSCustomerID": "all", "part_types": "MOTHERBOARD", "idCustomertype": "all", "MaterialType": "all"}, {"rule_id": 6112, "rule_name": "SidelineSpecialHandling", "rule_description": "A stakeholder has targeted this part for additional research, sideline for special handling", "priority": 4, "workflow_id": "10", "input_id": "130,131,132,133,134,135,136,137,138,139,140,141,142,149", "disposition_id": 34, "status": "Active", "rule_summary": "input = \"Pass-Evaluation OR Fail-DamagedBodyChassis OR Fail-DamagedChipsPins OR Fail-DamagedCords OR Fail-DamagedFixtures OR Fail-DamagedTestAccessPoint OR Fail-ExcessiveRust OR Fail-MissingCriticalPart OR Fail-DamagedOrMissingHeatSink OR Fail-FRUMissingCombination OR Fail-FRUMissingFan OR Fail-FRUMissingPSU OR Fail-OutdatedHeatSinkOrPins OR Fail-ImproperPackaging\" , Special Handling = \"Yes\"  , Facility = \"All\" ", "created_date": "2025-06-05 18:56:09", "created_by": 733, "updated_date": null, "updated_by": null, "FacilityID": "all", "sub_disposition_id": null, "version_id": 71, "rule_id_text": "BRE_Rework_Prod_Copy_v5-6112", "AWSCustomerID": "all", "part_types": "All", "idCustomertype": "all", "MaterialType": "all"}, {"rule_id": 6113, "rule_name": "SidelineRMADirect", "rule_description": "These parts should be returned to OEM vendor upon receipt", "priority": 5, "workflow_id": "10", "input_id": "130,131,132,133,134,135,136,137,138,139,140,141,142,149", "disposition_id": 5, "status": "Active", "rule_summary": "input = \"Pass-Evaluation OR Fail-DamagedBodyChassis OR Fail-DamagedChipsPins OR Fail-DamagedCords OR Fail-DamagedFixtures OR Fail-DamagedTestAccessPoint OR Fail-ExcessiveRust OR Fail-MissingCriticalPart OR Fail-DamagedOrMissingHeatSink OR Fail-FRUMissingCombination OR Fail-FRUMissingFan OR Fail-FRUMissingPSU OR Fail-OutdatedHeatSinkOrPins OR Fail-ImproperPackaging\" , RMA Direct = \"Yes\"  , Facility = \"All\" ", "created_date": "2025-06-05 18:56:09", "created_by": 733, "updated_date": "2025-06-05 18:56:52", "updated_by": 733, "FacilityID": "all", "sub_disposition_id": null, "version_id": 71, "rule_id_text": "BRE_Rework_Prod_Copy_v5-6113", "AWSCustomerID": "all", "part_types": "All", "idCustomertype": "all", "MaterialType": "all"}, {"rule_id": 6114, "rule_name": "RecycleP-WEEE", "rule_description": "Any part with sanitization coming from the DC-WEEE source type needs to be recycle processed", "priority": 6, "workflow_id": "10", "input_id": "131,132,133,134,138,135,136,139,140,141,149,137,142,130", "disposition_id": 27, "status": "Active", "rule_summary": "input = \"Fail-DamagedBodyChassis OR Fail-DamagedChipsPins OR Fail-DamagedCords OR Fail-DamagedFixtures OR Fail-DamagedOrMissingHeatSink OR Fail-DamagedTestAccessPoint OR Fail-ExcessiveRust OR Fail-FRUMissingCombination OR Fail-FRUMissingFan OR Fail-FRUMissingPSU OR Fail-ImproperPackaging OR Fail-MissingCriticalPart OR Fail-OutdatedHeatSinkOrPins OR Pass-Evaluation\" , Sanitization Flag = \"Yes\"  , Facility = \"All\" \"", "created_date": "2025-06-05 18:56:09", "created_by": 733, "updated_date": null, "updated_by": null, "FacilityID": "all", "sub_disposition_id": null, "version_id": 71, "rule_id_text": "BRE_Rework_Prod_Copy_v5-6114", "AWSCustomerID": "all", "part_types": "All", "idCustomertype": "16", "MaterialType": "all"}, {"rule_id": 6115, "rule_name": "LiquidationA-WEEE", "rule_description": "Any part coming from DC-WEE source type, and with liquidationActive attribute, will be pathed to Liquidation active", "priority": 7, "workflow_id": "10", "input_id": "131,132,133,134,138,135,136,139,140,141,149,137,142,130", "disposition_id": 22, "status": "Active", "rule_summary": "input = \"Fail-DamagedBodyChassis OR Fail-DamagedChipsPins OR Fail-DamagedCords OR Fail-DamagedFixtures OR Fail-DamagedOrMissingHeatSink OR Fail-DamagedTestAccessPoint OR Fail-ExcessiveRust OR Fail-FRUMissingCombination OR Fail-FRUMissingFan OR Fail-FRUMissingPSU OR Fail-ImproperPackaging OR Fail-MissingCriticalPart OR Fail-OutdatedHeatSinkOrPins OR Pass-Evaluation\" , Attribute ID Contains \"Liquidation-Active\"  , Facility = \"All\" \"", "created_date": "2025-06-05 18:56:10", "created_by": 733, "updated_date": null, "updated_by": null, "FacilityID": "all", "sub_disposition_id": null, "version_id": 71, "rule_id_text": "BRE_Rework_Prod_Copy_v5-6115", "AWSCustomerID": "all", "part_types": "All", "idCustomertype": "16", "MaterialType": "all"}, {"rule_id": 6116, "rule_name": "RecycleS-WEEE", "rule_description": "Catchall for any other parts coming from DC-WEEE source type that aren't RP or LiqA", "priority": 8, "workflow_id": "10", "input_id": "130,131,132,133,134,138,135,136,139,140,141,149,137,142", "disposition_id": 10, "status": "Active", "rule_summary": "input = \"Pass-Evaluation OR Fail-DamagedBodyChassis OR Fail-DamagedChipsPins OR Fail-DamagedCords OR Fail-DamagedFixtures OR Fail-DamagedOrMissingHeatSink OR Fail-DamagedTestAccessPoint OR Fail-ExcessiveRust OR Fail-FRUMissingCombination OR Fail-FRUMissingFan OR Fail-FRUMissingPSU OR Fail-ImproperPackaging OR Fail-MissingCriticalPart OR Fail-OutdatedHeatSinkOrPins\" , , Facility = \"All\" ", "created_date": "2025-06-05 18:56:10", "created_by": 733, "updated_date": null, "updated_by": null, "FacilityID": "all", "sub_disposition_id": null, "version_id": 71, "rule_id_text": "BRE_Rework_Prod_Copy_v5-6116", "AWSCustomerID": "all", "part_types": "All", "idCustomertype": "16", "MaterialType": "all"}, {"rule_id": 6117, "rule_name": "HighPriorityFA", "rule_description": "This parts has been identified as Critical or High Priority and should be tested first based on demand.", "priority": 9, "workflow_id": "10", "input_id": "130", "disposition_id": 97, "status": "Active", "rule_summary": "input = \"Pass-Evaluation\" , Priority Type = \"Critical\" , First Arrival = \"Yes\" , Attribute ID Contains \"Failure_Analysis-FullTest\"  , Facility = \"All\" ", "created_date": "2025-06-05 18:56:10", "created_by": 733, "updated_date": null, "updated_by": null, "FacilityID": "all", "sub_disposition_id": null, "version_id": 71, "rule_id_text": "BRE_Rework_Prod_Copy_v5-6117", "AWSCustomerID": "all", "part_types": "AWS OPTICAL MODULE,RAM", "idCustomertype": "all", "MaterialType": "all"}, {"rule_id": 6118, "rule_name": "<PERSON><PERSON>_DongleFA", "rule_description": "Part qualifies for FA, route to FA Lab from demanufacture operation", "priority": 10, "workflow_id": "10", "input_id": "130", "disposition_id": 2, "status": "Active", "rule_summary": "input = \"Pass-Evaluation\" , Attribute ID Contains \"Failure_Analysis-FullTest\" , Fleet Risk = \"No\" , Demand Flag = \"Yes\"  , Facility = \"All\" ", "created_date": "2025-06-05 18:56:10", "created_by": 733, "updated_date": null, "updated_by": null, "FacilityID": "all", "sub_disposition_id": null, "version_id": 71, "rule_id_text": "BRE_Rework_Prod_Copy_v5-6118", "AWSCustomerID": "all", "part_types": "DONGLE", "idCustomertype": "2", "MaterialType": "all"}, {"rule_id": 6119, "rule_name": "Deman_SwitchFA", "rule_description": "Part qualifies for FA, route to FA Lab from demanufacture operation", "priority": 11, "workflow_id": "10", "input_id": "130", "disposition_id": 2, "status": "Active", "rule_summary": "input = \"Pass-Evaluation\" , First Arrival = \"Yes\" , Fleet Risk = \"No\" , Demand Flag = \"Yes\" , Attribute ID Contains \"Failure_Analysis-FullTest\"  , Facility = \"All\" \"", "created_date": "2025-06-05 18:56:10", "created_by": 733, "updated_date": null, "updated_by": null, "FacilityID": "all", "sub_disposition_id": null, "version_id": 71, "rule_id_text": "BRE_Rework_Prod_Copy_v5-6119", "AWSCustomerID": "all", "part_types": "AWS SWITCH", "idCustomertype": "2", "MaterialType": "all"}, {"rule_id": 6120, "rule_name": "Deman_CPU_Liquidation", "rule_description": "Part qualifies for liquidation, route to liquidation from demanufacture operation", "priority": 12, "workflow_id": "10", "input_id": "130", "disposition_id": 22, "status": "Active", "rule_summary": "input = \"Pass-Evaluation\" , Sanitization Flag = \"No\" , Attribute ID Contains \"Liquidation-Active\"  , Facility = \"All\" ", "created_date": "2025-06-05 18:56:10", "created_by": 733, "updated_date": null, "updated_by": null, "FacilityID": "all", "sub_disposition_id": null, "version_id": 71, "rule_id_text": "BRE_Rework_Prod_Copy_v5-6120", "AWSCustomerID": "all", "part_types": "CPU", "idCustomertype": "2", "MaterialType": "all"}, {"rule_id": 6121, "rule_name": "Deman_RAM_Liquidation", "rule_description": "Part qualifies for liquidation, route to liquidation from demanufacture operation", "priority": 13, "workflow_id": "10", "input_id": "130", "disposition_id": 22, "status": "Active", "rule_summary": "input = \"Pass-Evaluation\" , Sanitization Flag = \"No\" , Attribute ID Contains \"Liquidation-Active\"  , Facility = \"All\" ", "created_date": "2025-06-05 18:56:10", "created_by": 733, "updated_date": null, "updated_by": null, "FacilityID": "all", "sub_disposition_id": null, "version_id": 71, "rule_id_text": "BRE_Rework_Prod_Copy_v5-6121", "AWSCustomerID": "all", "part_types": "RAM", "idCustomertype": "2", "MaterialType": "all"}, {"rule_id": 6122, "rule_name": "<PERSON><PERSON>_Switch_Recovery", "rule_description": "This part requires disassembly, sideline for parts recovery", "priority": 14, "workflow_id": "10", "input_id": "130,131,132,133,134,135,136,137,138,139,140,141,142,149", "disposition_id": 94, "status": "Active", "rule_summary": "input = \"Pass-Evaluation OR Fail-DamagedBodyChassis OR Fail-DamagedChipsPins OR Fail-DamagedCords OR Fail-DamagedFixtures OR Fail-DamagedTestAccessPoint OR Fail-ExcessiveRust OR Fail-MissingCriticalPart OR Fail-DamagedOrMissingHeatSink OR Fail-FRUMissingCombination OR Fail-FRUMissingFan OR Fail-FRUMissingPSU OR Fail-OutdatedHeatSinkOrPins OR Fail-ImproperPackaging\" , , Facility = \"All\" ", "created_date": "2025-06-05 18:56:10", "created_by": 733, "updated_date": null, "updated_by": null, "FacilityID": "all", "sub_disposition_id": null, "version_id": 71, "rule_id_text": "BRE_Rework_Prod_Copy_v5-6122", "AWSCustomerID": "all", "part_types": "AWS SWITCH", "idCustomertype": "2", "MaterialType": "all"}, {"rule_id": 6123, "rule_name": "<PERSON><PERSON>_Motherboard_Recycle", "rule_description": "There is no higher recovery opportunity for this part, send to outbound shipping for recycling removal", "priority": 15, "workflow_id": "10", "input_id": "130,131,132,133,134,135,136,137,138,139,140,141,142,149", "disposition_id": 10, "status": "Active", "rule_summary": "input = \"Pass-Evaluation OR Fail-DamagedBodyChassis OR Fail-DamagedChipsPins OR Fail-DamagedCords OR Fail-DamagedFixtures OR Fail-DamagedTestAccessPoint OR Fail-ExcessiveRust OR Fail-MissingCriticalPart OR Fail-DamagedOrMissingHeatSink OR Fail-FRUMissingCombination OR Fail-FRUMissingFan OR Fail-FRUMissingPSU OR Fail-OutdatedHeatSinkOrPins OR Fail-ImproperPackaging\" , Sanitization Flag = \"No\"  , Facility = \"All\" ", "created_date": "2025-06-05 18:56:10", "created_by": 733, "updated_date": null, "updated_by": null, "FacilityID": "all", "sub_disposition_id": null, "version_id": 71, "rule_id_text": "BRE_Rework_Prod_Copy_v5-6123", "AWSCustomerID": "all", "part_types": "MOTHERBOARD", "idCustomertype": "2", "MaterialType": "all"}, {"rule_id": 6124, "rule_name": "Deman_CPU_Recycle", "rule_description": "There is no higher recovery opportunity for this part, route to recycling removal", "priority": 16, "workflow_id": "10", "input_id": "130", "disposition_id": 10, "status": "Active", "rule_summary": "input = \"Pass-Evaluation\" , Sanitization Flag = \"No\"  , Facility = \"All\" \"", "created_date": "2025-06-05 18:56:10", "created_by": 733, "updated_date": null, "updated_by": null, "FacilityID": "all", "sub_disposition_id": null, "version_id": 71, "rule_id_text": "BRE_Rework_Prod_Copy_v5-6124", "AWSCustomerID": "all", "part_types": "CPU", "idCustomertype": "2", "MaterialType": "all"}, {"rule_id": 6125, "rule_name": "<PERSON>man_RAM_Investigation", "rule_description": "When a RAM part type has a Sanitization = \"Yes\" attribute, we want to ensure that they truly need to be destroyed before losing valuable products.", "priority": 17, "workflow_id": "10", "input_id": "130", "disposition_id": 98, "status": "Active", "rule_summary": "input = \"Pass-Evaluation\" , Sanitization Flag = \"Yes\"  , Facility = \"All\" ", "created_date": "2025-06-05 18:56:10", "created_by": 733, "updated_date": null, "updated_by": null, "FacilityID": "all", "sub_disposition_id": null, "version_id": 71, "rule_id_text": "BRE_Rework_Prod_Copy_v5-6125", "AWSCustomerID": "all", "part_types": "RAM", "idCustomertype": "2", "MaterialType": "all"}, {"rule_id": 6126, "rule_name": "<PERSON>man_CPU_Destruction", "rule_description": "There is no higher recovery opportunity for this part, consolidate for local destructive sanitization", "priority": 18, "workflow_id": "10", "input_id": "131,132,133,134,138,135,136,139,140,141,149,137,142", "disposition_id": 27, "status": "Active", "rule_summary": "input = \"Fail-DamagedBodyChassis OR Fail-DamagedChipsPins OR Fail-DamagedCords OR Fail-DamagedFixtures OR Fail-DamagedOrMissingHeatSink OR Fail-DamagedTestAccessPoint OR Fail-ExcessiveRust OR Fail-FRUMissingCombination OR Fail-FRUMissingFan OR Fail-FRUMissingPSU OR Fail-ImproperPackaging OR Fail-MissingCriticalPart OR Fail-OutdatedHeatSinkOrPins\" , Sanitization Flag = \"Yes\"  , Facility = \"All\" \"", "created_date": "2025-06-05 18:56:10", "created_by": 733, "updated_date": null, "updated_by": null, "FacilityID": "all", "sub_disposition_id": null, "version_id": 71, "rule_id_text": "BRE_Rework_Prod_Copy_v5-6126", "AWSCustomerID": "all", "part_types": "CPU", "idCustomertype": "2", "MaterialType": "all"}, {"rule_id": 6127, "rule_name": "Deman_RAM_LiquidationDamaged", "rule_description": "We will be repairing damaged DIMMs for liquidation, route these parts to the appropriate repair process", "priority": 19, "workflow_id": "10", "input_id": "131,132,133,134,135,136,137,138,139,140,141,142,149", "disposition_id": 99, "status": "Active", "rule_summary": "input = \"Fail-DamagedBodyChassis OR Fail-DamagedChipsPins OR Fail-DamagedCords OR Fail-DamagedFixtures OR Fail-DamagedTestAccessPoint OR Fail-ExcessiveRust OR Fail-MissingCriticalPart OR Fail-DamagedOrMissingHeatSink OR Fail-FRUMissingCombination OR Fail-FRUMissingFan OR Fail-FRUMissingPSU OR Fail-OutdatedHeatSinkOrPins OR Fail-ImproperPackaging\" , , Facility = \"All\" ", "created_date": "2025-06-05 18:56:10", "created_by": 733, "updated_date": null, "updated_by": null, "FacilityID": "all", "sub_disposition_id": null, "version_id": 71, "rule_id_text": "BRE_Rework_Prod_Copy_v5-6127", "AWSCustomerID": "all", "part_types": "RAM", "idCustomertype": "2", "MaterialType": "all"}, {"rule_id": 6128, "rule_name": "FailureAnalysis-6", "rule_description": "This repaired part should be tested to determine suitability in the fleet, send to failure analysis testing", "priority": 34, "workflow_id": "7", "input_id": "122", "disposition_id": 2, "status": "Active", "rule_summary": "input = \"Pass-RemoveReplaceThermalPastePins\" , Attribute ID Contains \"Repair-HeatSinkReplacement\" , Demand Flag = \"Yes\"  , Facility = \"All\" \"", "created_date": "2025-06-05 18:56:10", "created_by": 733, "updated_date": null, "updated_by": null, "FacilityID": "all", "sub_disposition_id": null, "version_id": 71, "rule_id_text": "BRE_Rework_Prod_Copy_v5-6128", "AWSCustomerID": "all", "part_types": "DONGLE", "idCustomertype": "all", "MaterialType": "all"}, {"rule_id": 6129, "rule_name": "FailureAnalysis-11", "rule_description": "This repaired part should be tested to determine suitability in the fleet, send to failure analysis testing", "priority": 35, "workflow_id": "7", "input_id": "81", "disposition_id": 2, "status": "Active", "rule_summary": "input = \"Pass-Repair\" , Demand Flag = \"Yes\"  , Facility = \"All\" \"", "created_date": "2025-06-05 18:56:10", "created_by": 733, "updated_date": null, "updated_by": null, "FacilityID": "all", "sub_disposition_id": null, "version_id": 71, "rule_id_text": "BRE_Rework_Prod_Copy_v5-6129", "AWSCustomerID": "all", "part_types": "All", "idCustomertype": "all", "MaterialType": "all"}, {"rule_id": 6130, "rule_name": "Sanitization-Spare", "rule_description": "This part passed failure analysis testing and requires sanitization before shipping outbound to satisfy a spares demand, send to sanitization", "priority": 36, "workflow_id": "2", "input_id": "10", "disposition_id": 4, "status": "Active", "rule_summary": "input = \"Pass-Testing\" , Attribute ID Contains \"Sanitization-Spare\" , Demand Flag = \"Yes\" , Sanitization Flag = \"Yes\"  , Facility = \"All\" ", "created_date": "2025-06-05 18:56:10", "created_by": 733, "updated_date": null, "updated_by": null, "FacilityID": "all", "sub_disposition_id": null, "version_id": 71, "rule_id_text": "BRE_Rework_Prod_Copy_v5-6130", "AWSCustomerID": "all", "part_types": "All", "idCustomertype": "all", "MaterialType": "all"}, {"rule_id": 6131, "rule_name": "Sanitization-NetworkGear", "rule_description": "Route various network gear to sanitization if they have sanitization requirement", "priority": 37, "workflow_id": "10", "input_id": "130", "disposition_id": 4, "status": "Active", "rule_summary": "input = \"Pass-Evaluation\" , Attribute ID Not Contains \"Failure_Analysis-FullTest\" , Sanitization Flag = \"Yes\" , Attribute ID Contains \"Sanitization-Recycle OR Sanitization-Destruction OR Sanitization-RMA OR Sanitization-Liquidation\"  , Facility = \"All\" ", "created_date": "2025-06-05 18:56:10", "created_by": 733, "updated_date": "2025-06-05 18:58:12", "updated_by": 733, "FacilityID": "all", "sub_disposition_id": null, "version_id": 71, "rule_id_text": "BRE_Rework_Prod_Copy_v5-6131", "AWSCustomerID": "all", "part_types": "AWS DWDM,<PERSON>WS ROUTER,AWS SWITCH,CHASSIS,AWS NETWORK ACCESSORY", "idCustomertype": "all", "MaterialType": "all"}, {"rule_id": 6132, "rule_name": "Spare-3", "rule_description": "This part passed failure analysis testing and non-destructive sanitization, send to outbound shipping to satisfy a spares demand", "priority": 38, "workflow_id": "3", "input_id": "24", "disposition_id": 3, "status": "Active", "rule_summary": "input = \"Pass-Sanitization\" , Attribute ID Contains \"Sanitization-Spare\" , Demand Flag = \"Yes\"  , Facility = \"All\" ", "created_date": "2025-06-05 18:56:10", "created_by": 733, "updated_date": null, "updated_by": null, "FacilityID": "all", "sub_disposition_id": null, "version_id": 71, "rule_id_text": "BRE_Rework_Prod_Copy_v5-6132", "AWSCustomerID": "all", "part_types": "All", "idCustomertype": "all", "MaterialType": "all"}, {"rule_id": 6133, "rule_name": "FastTrack-Spare-1", "rule_description": "This is a spare replacement part sent from the vendor, send to VMI or direct ship to DC as a spare part", "priority": 20, "workflow_id": "10", "input_id": "130", "disposition_id": 3, "status": "Active", "rule_summary": "input = \"Pass-Evaluation\" , First Arrival = \"Yes\" , Fleet Risk = \"No\" , Demand Flag = \"Yes\"  , Facility = \"All\" ", "created_date": "2025-06-05 18:56:10", "created_by": 733, "updated_date": null, "updated_by": null, "FacilityID": "all", "sub_disposition_id": null, "version_id": 71, "rule_id_text": "BRE_Rework_Prod_Copy_v5-6133", "AWSCustomerID": "all", "part_types": "All", "idCustomertype": "3", "MaterialType": "all"}, {"rule_id": 6134, "rule_name": "RMA-Sanitized", "rule_description": "Route sanitized DWDMs to RMA-Direct if RMA eligible", "priority": 39, "workflow_id": "3", "input_id": "24", "disposition_id": 100, "status": "Active", "rule_summary": "input = \"Pass-Sanitization\" , Attribute ID Contains \"RMA-Standard\" , Attribute ID Contains \"Sanitization-RMA\"  , Facility = \"All\" ", "created_date": "2025-06-05 18:56:10", "created_by": 733, "updated_date": null, "updated_by": null, "FacilityID": "all", "sub_disposition_id": null, "version_id": 71, "rule_id_text": "BRE_Rework_Prod_Copy_v5-6134", "AWSCustomerID": "all", "part_types": "All", "idCustomertype": "all", "MaterialType": "all"}, {"rule_id": 6135, "rule_name": "Liquidation-Sanitized", "rule_description": "Route sanitized network gear to liquidation if they are liquidation eligible", "priority": 40, "workflow_id": "3", "input_id": "24", "disposition_id": 22, "status": "Active", "rule_summary": "input = \"Pass-Sanitization\" , Attribute ID Contains \"Liquidation-Active\" , Attribute ID Contains \"Sanitization-Liquidation\"  , Facility = \"All\" ", "created_date": "2025-06-05 18:56:10", "created_by": 733, "updated_date": null, "updated_by": null, "FacilityID": "all", "sub_disposition_id": null, "version_id": 71, "rule_id_text": "BRE_Rework_Prod_Copy_v5-6135", "AWSCustomerID": "all", "part_types": "All", "idCustomertype": "all", "MaterialType": "all"}, {"rule_id": 6136, "rule_name": "FastTrack-Repair-1", "rule_description": "This part qualifies for repair in effort to test and spare, send to repair", "priority": 21, "workflow_id": "10", "input_id": "138,142", "disposition_id": 19, "status": "Active", "rule_summary": "input = \"Fail-DamagedOrMissingHeatSink OR Fail-OutdatedHeatSinkOrPins\" , Attribute ID Contains \"Failure_Analysis-FullTest\" , Fleet Risk = \"No\" , Attribute ID Contains \"Repair-HeatSinkReplacement\" , Demand Flag = \"Yes\"  , Facility = \"All\" ", "created_date": "2025-06-05 18:56:10", "created_by": 733, "updated_date": null, "updated_by": null, "FacilityID": "all", "sub_disposition_id": null, "version_id": 71, "rule_id_text": "BRE_Rework_Prod_Copy_v5-6136", "AWSCustomerID": "all", "part_types": "GPU,DONGLE", "idCustomertype": "all", "MaterialType": "all"}, {"rule_id": 6137, "rule_name": "ReturnMerchandiseAuthorization-2", "rule_description": "This will allow GPU (Nvidia) that fail testing to reroute to RMA Investigation", "priority": 41, "workflow_id": "all", "input_id": "1,11,12,13,14,15,16,17,18,19,92,99,100,101,102,103,120,123,126,127,128,130", "disposition_id": 5, "status": "Active", "rule_summary": "input = \"Pass-Evaluation OR Fail-Testing OR Fail-AuthenticationError OR Fail-FlapRecord OR Fail-IncompatibleForTesting OR Fail-MultipleTestErrors OR Fail-MultipleTestFail OR Fail-NoPower OR Fail-PartNotFound OR Fail-RetiredPart OR Fail-PortFailure OR Fail-FRUFailureCombination OR Fail-FRUFailureFan OR Fail-FRUFailurePSU OR Fail-FRUFailureRAM OR Fail-TestingAfterMultipleRepairs OR Fail-Overheat OR Fail-BrickUnit OR Fail-FlapReocrd OR Fail-RepeatOffender OR Fail-FA2Candidate OR Pass-Evaluation\" , Fleet Risk = \"No\" , Demand Flag = \"Yes\" , Attribute ID Contains \"RMA-Standard\" , Manufacturer = \"1024\"  , Facility = \"All\" ", "created_date": "2025-06-05 18:56:10", "created_by": 733, "updated_date": null, "updated_by": null, "FacilityID": "all", "sub_disposition_id": null, "version_id": 71, "rule_id_text": "BRE_Rework_Prod_Copy_v5-6137", "AWSCustomerID": "all", "part_types": "GPU,GPU BASEBOARD,GPU BOARD ASSEMBLY", "idCustomertype": "all", "MaterialType": "all"}, {"rule_id": 6138, "rule_name": "FastTrack-Repair-6", "rule_description": "This part qualifies for repair in effort to test and spare, send to repair", "priority": 22, "workflow_id": "10", "input_id": "130", "disposition_id": 19, "status": "Active", "rule_summary": "input = \"Pass-Evaluation\" , First Arrival = \"Yes\" , Fleet Risk = \"No\" , Attribute ID Contains \"Failure_Analysis-FullTest\" , Attribute ID Contains \"Repair-HeatSinkReplacement\" , Demand Flag = \"Yes\"  , Facility = \"All\" ", "created_date": "2025-06-05 18:56:10", "created_by": 733, "updated_date": null, "updated_by": null, "FacilityID": "all", "sub_disposition_id": null, "version_id": 71, "rule_id_text": "BRE_Rework_Prod_Copy_v5-6138", "AWSCustomerID": "all", "part_types": "GPU", "idCustomertype": "all", "MaterialType": "all"}, {"rule_id": 6139, "rule_name": "FailureAnalysis-DongleBypass", "rule_description": "This rule bypasses the FIRST ARRIVAL for Dongles only and allows them to be sent to FA.", "priority": 23, "workflow_id": "10", "input_id": "130", "disposition_id": 2, "status": "Active", "rule_summary": "input = \"Pass-Evaluation\" , Attribute ID Contains \"Failure_Analysis-FullTest\" , Demand Flag = \"Yes\" , Fleet Risk = \"No\"  , Facility = \"All\" \"", "created_date": "2025-06-05 18:56:10", "created_by": 733, "updated_date": null, "updated_by": null, "FacilityID": "all", "sub_disposition_id": null, "version_id": 71, "rule_id_text": "BRE_Rework_Prod_Copy_v5-6139", "AWSCustomerID": "all", "part_types": "DONGLE", "idCustomertype": "all", "MaterialType": "all"}, {"rule_id": 6140, "rule_name": "ReturnMerchandiseAuthorization-1", "rule_description": "This part may qualify for a free replacement, send to RMA for investigation", "priority": 42, "workflow_id": "all", "input_id": "1,11,12,13,14,15,16,17,18,19,92,99,100,101,102,103,120,123,126,127,128,130", "disposition_id": 5, "status": "Active", "rule_summary": "input = \"Pass-Evaluation OR Fail-Testing OR Fail-AuthenticationError OR Fail-FlapRecord OR Fail-IncompatibleForTesting OR Fail-MultipleTestErrors OR Fail-MultipleTestFail OR Fail-NoPower OR Fail-PartNotFound OR Fail-RetiredPart OR Fail-PortFailure OR Fail-FRUFailureCombination OR Fail-FRUFailureFan OR Fail-FRUFailurePSU OR Fail-FRUFailureRAM OR Fail-TestingAfterMultipleRepairs OR Fail-Overheat OR Fail-BrickUnit OR Fail-FlapReocrd OR Fail-RepeatOffender OR Fail-FA2Candidate OR Pass-Evaluation\" , Demand Flag = \"Yes\" , First Arrival = \"Yes\" , Fleet Risk = \"No\" , In Warranty = \"Yes\" , Attribute ID Contains \"RMA-Standard\" , Sanitization Flag = \"No\"  , Facility = \"All\" ", "created_date": "2025-06-05 18:56:10", "created_by": 733, "updated_date": null, "updated_by": null, "FacilityID": "all", "sub_disposition_id": null, "version_id": 71, "rule_id_text": "BRE_Rework_Prod_Copy_v5-6140", "AWSCustomerID": "all", "part_types": "All", "idCustomertype": "all", "MaterialType": "all"}, {"rule_id": 6141, "rule_name": "FailureAnalysis-1", "rule_description": "There is a need to test this part, send to failure analysis for testing", "priority": 24, "workflow_id": "10", "input_id": "130", "disposition_id": 2, "status": "Active", "rule_summary": "input = \"Pass-Evaluation\" , Attribute ID Contains \"Failure_Analysis-FullTest\" , Demand Flag = \"Yes\" , First Arrival = \"Yes\" , Fleet Risk = \"No\"  , Facility = \"All\" ", "created_date": "2025-06-05 18:56:10", "created_by": 733, "updated_date": null, "updated_by": null, "FacilityID": "all", "sub_disposition_id": null, "version_id": 71, "rule_id_text": "BRE_Rework_Prod_Copy_v5-6141", "AWSCustomerID": "all", "part_types": "All", "idCustomertype": "all", "MaterialType": "all"}, {"rule_id": 6142, "rule_name": "FailureAnalysis-PSU_FleetRisk_Bypass", "rule_description": "Allows fleet risk PSUs through to test for RMA", "priority": 25, "workflow_id": "10", "input_id": "130", "disposition_id": 2, "status": "Active", "rule_summary": "input = \"Pass-Evaluation\" , Attribute ID Contains \"Failure_Analysis-FullTest\" , Demand Flag = \"Yes\" , First Arrival = \"Yes\"  , Facility = \"All\" \"", "created_date": "2025-06-05 18:56:10", "created_by": 733, "updated_date": null, "updated_by": null, "FacilityID": "all", "sub_disposition_id": null, "version_id": 71, "rule_id_text": "BRE_Rework_Prod_Copy_v5-6142", "AWSCustomerID": "all", "part_types": "PSU", "idCustomertype": "all", "MaterialType": "all"}, {"rule_id": 6143, "rule_name": "ReturnToVendor-PartOfAssembly", "rule_description": "Need to route GPUs, which are part of board assemblies, to new POA disposition to enable removal for RMA and accurate counts for commercial", "priority": 43, "workflow_id": "4", "input_id": "156", "disposition_id": 95, "status": "Active", "rule_summary": "input = \"Pass-ReturnToVendorRMA-PARTOFASSEMBLY\" , , Facility = \"All\" ", "created_date": "2025-06-05 18:56:10", "created_by": 733, "updated_date": null, "updated_by": null, "FacilityID": "all", "sub_disposition_id": null, "version_id": 71, "rule_id_text": "BRE_Rework_Prod_Copy_v5-6143", "AWSCustomerID": "all", "part_types": "GPU", "idCustomertype": "all", "MaterialType": "all"}, {"rule_id": 6144, "rule_name": "FailureAnalysis-2", "rule_description": "There is a need to test this part, send to failure analysis for testing", "priority": 26, "workflow_id": "10", "input_id": "130", "disposition_id": 2, "status": "Active", "rule_summary": "input = \"Pass-Evaluation\" , Attribute ID Contains \"Failure_Analysis-FullTest\" , First Arrival = \"Yes\" , Attribute ID Contains \"RMA-Virtual\"  , Facility = \"All\" ", "created_date": "2025-06-05 18:56:10", "created_by": 733, "updated_date": null, "updated_by": null, "FacilityID": "all", "sub_disposition_id": null, "version_id": 71, "rule_id_text": "BRE_Rework_Prod_Copy_v5-6144", "AWSCustomerID": "all", "part_types": "All", "idCustomertype": "all", "MaterialType": "all"}, {"rule_id": 6145, "rule_name": "FailureAnalysis-3", "rule_description": "This part needs to be tested again", "priority": 27, "workflow_id": "2", "input_id": "20,21,22,23", "disposition_id": 2, "status": "Active", "rule_summary": "input = \"Retest-FirstTestError OR Retest-SecondTestError OR Retest-FirstTestFail OR Retest-SecondTestFail\" , , Facility = \"All\" ", "created_date": "2025-06-05 18:56:10", "created_by": 733, "updated_date": null, "updated_by": null, "FacilityID": "all", "sub_disposition_id": null, "version_id": 71, "rule_id_text": "BRE_Rework_Prod_Copy_v5-6145", "AWSCustomerID": "all", "part_types": "All", "idCustomertype": "all", "MaterialType": "all"}, {"rule_id": 6146, "rule_name": "Spare-1", "rule_description": "This part passed failure analysis testing and satisfies a spares demand, send to VMI or direct ship to DC as a spare part", "priority": 28, "workflow_id": "2", "input_id": "10", "disposition_id": 3, "status": "Active", "rule_summary": "input = \"Pass-Testing\" , Demand Flag = \"Yes\" , Sanitization Flag = \"No\" , Fleet Risk = \"No\"  , Facility = \"All\" ", "created_date": "2025-06-05 18:56:10", "created_by": 733, "updated_date": null, "updated_by": null, "FacilityID": "all", "sub_disposition_id": null, "version_id": 71, "rule_id_text": "BRE_Rework_Prod_Copy_v5-6146", "AWSCustomerID": "all", "part_types": "All", "idCustomertype": "all", "MaterialType": "all"}, {"rule_id": 6147, "rule_name": "RMAPartInWarranty-GPU_Sani_Bypass", "rule_description": "Allows GPUs with sanitization out to RMA", "priority": 45, "workflow_id": "4", "input_id": "118,156", "disposition_id": 6, "status": "Active", "rule_summary": "input = \"Pass-RMAWarrantyCheck OR Pass-ReturnToVendorRMA-PARTOFASSEMBLY\" , Sanitization Flag = \"Yes\" , Manufacturer = \"1024\"  , Facility = \"All\" ", "created_date": "2025-06-05 18:56:10", "created_by": 733, "updated_date": null, "updated_by": null, "FacilityID": "all", "sub_disposition_id": null, "version_id": 71, "rule_id_text": "BRE_Rework_Prod_Copy_v5-6147", "AWSCustomerID": "all", "part_types": "GPU BASEBOARD,GPU,GPU BOARD ASSEMBLY", "idCustomertype": "all", "MaterialType": "all"}, {"rule_id": 6148, "rule_name": "Spare-2", "rule_description": "This part passed failure analysis testing and satisfies a spares demand, send to VMI or direct ship to DC as a spare part", "priority": 29, "workflow_id": "2", "input_id": "10", "disposition_id": 3, "status": "Active", "rule_summary": "input = \"Pass-Testing\" , Demand Flag = \"Yes\" , Sanitization Flag = \"Yes\"  , Facility = \"All\" ", "created_date": "2025-06-05 18:56:10", "created_by": 733, "updated_date": null, "updated_by": null, "FacilityID": "all", "sub_disposition_id": null, "version_id": 71, "rule_id_text": "BRE_Rework_Prod_Copy_v5-6148", "AWSCustomerID": "all", "part_types": "DONGLE", "idCustomertype": "all", "MaterialType": "all"}, {"rule_id": 6149, "rule_name": "SpareTransfer-1", "rule_description": "This part passed failure analysis testing and satisfies a spares demand, send to DC as a spare part via Red Zone Transfer ONLY", "priority": 30, "workflow_id": "2", "input_id": "10", "disposition_id": 50, "status": "Active", "rule_summary": "input = \"Pass-Testing\" , Attribute ID Not Contains \"Sanitization-Spare\" , Demand Flag = \"Yes\" , Sanitization Flag = \"Yes\"  , Facility = \"All\" ", "created_date": "2025-06-05 18:56:10", "created_by": 733, "updated_date": null, "updated_by": null, "FacilityID": "all", "sub_disposition_id": null, "version_id": 71, "rule_id_text": "BRE_Rework_Prod_Copy_v5-6149", "AWSCustomerID": "all", "part_types": "All", "idCustomertype": "all", "MaterialType": "all"}, {"rule_id": 6150, "rule_name": "Repair-1", "rule_description": "This part requires repairs before failure analysis testing, send to repair", "priority": 31, "workflow_id": "2", "input_id": "120", "disposition_id": 19, "status": "Active", "rule_summary": "input = \"Fail-Overheat\" , Attribute ID Contains \"Repair-HeatSinkReplacement\" , Demand Flag = \"Yes\"  , Facility = \"All\" ", "created_date": "2025-06-05 18:56:10", "created_by": 733, "updated_date": null, "updated_by": null, "FacilityID": "all", "sub_disposition_id": null, "version_id": 71, "rule_id_text": "BRE_Rework_Prod_Copy_v5-6150", "AWSCustomerID": "all", "part_types": "DONGLE", "idCustomertype": "all", "MaterialType": "all"}, {"rule_id": 6151, "rule_name": "ReturnToVendor-ReturnMerchandiseAuthorization-1", "rule_description": "This part qualifies for free replacement, send to outbound shipping to return to the vendor", "priority": 46, "workflow_id": "4", "input_id": "118,156", "disposition_id": 6, "status": "Active", "rule_summary": "input = \"Pass-RMAWarrantyCheck OR Pass-ReturnToVendorRMA-PARTOFASSEMBLY\" , Sanitization Flag = \"No\"  , Facility = \"All\" \"", "created_date": "2025-06-05 18:56:10", "created_by": 733, "updated_date": null, "updated_by": null, "FacilityID": "all", "sub_disposition_id": null, "version_id": 71, "rule_id_text": "BRE_Rework_Prod_Copy_v5-6151", "AWSCustomerID": "all", "part_types": "All", "idCustomertype": "all", "MaterialType": "all"}, {"rule_id": 6152, "rule_name": "FailureAnalysisLevel2-1", "rule_description": "This part requires further analysis, send to outbound shipping for delivery to FA2", "priority": 47, "workflow_id": "2", "input_id": "128", "disposition_id": 51, "status": "Active", "rule_summary": "input = \"Fail-FA2Candidate\" , Sanitization Flag = \"No\"  , Facility = \"All\" ", "created_date": "2025-06-05 18:56:10", "created_by": 733, "updated_date": null, "updated_by": null, "FacilityID": "all", "sub_disposition_id": null, "version_id": 71, "rule_id_text": "BRE_Rework_Prod_Copy_v5-6152", "AWSCustomerID": "all", "part_types": "AWS Cable,AWS OPTICAL MODULE,AWS FIBER", "idCustomertype": "1", "MaterialType": "all"}, {"rule_id": 6154, "rule_name": "FailureAnalysisLevel2-2", "rule_description": "This part requires further analysis, send to outbound shipping for delivery to FA2", "priority": 48, "workflow_id": "2", "input_id": "13", "disposition_id": 51, "status": "Active", "rule_summary": "input = \"Fail-FlapRecord\" , Sanitization Flag = \"No\"  , Facility = \"All\" ", "created_date": "2025-06-05 18:56:10", "created_by": 733, "updated_date": "2025-06-05 18:59:37", "updated_by": 733, "FacilityID": "all", "sub_disposition_id": null, "version_id": 71, "rule_id_text": "BRE_Rework_Prod_Copy_v5-6154", "AWSCustomerID": "all", "part_types": "AWS Cable,AWS FIBER,AWS OPTICAL MODULE", "idCustomertype": "1", "MaterialType": "all"}, {"rule_id": 6155, "rule_name": "FailureAnalysisLevel2-3", "rule_description": "This part requires further analysis, send to outbound shipping for delivery to FA2", "priority": 49, "workflow_id": "2", "input_id": "127", "disposition_id": 51, "status": "Active", "rule_summary": "input = \"Fail-RepeatOffender\" , Sanitization Flag = \"No\"  , Facility = \"All\" ", "created_date": "2025-06-05 18:56:10", "created_by": 733, "updated_date": "2025-06-05 19:00:05", "updated_by": 733, "FacilityID": "all", "sub_disposition_id": null, "version_id": 71, "rule_id_text": "BRE_Rework_Prod_Copy_v5-6155", "AWSCustomerID": "all", "part_types": "AWS FIBER,AWS Cable,AWS OPTICAL MODULE", "idCustomertype": "1", "MaterialType": "all"}, {"rule_id": 6156, "rule_name": "Liquidation-1", "rule_description": "This part qualifies for liquidation, send to outbound for consolidation to liquidation vendor", "priority": 50, "workflow_id": "all", "input_id": "1,10,11,12,13,14,15,16,17,18,19,81,92,99,100,101,102,103,105,106,107,108,109,110,111,112,113,114,118,120,121,122,123,126,127,128,130,156", "disposition_id": 22, "status": "Active", "rule_summary": "input = \"Pass-Evaluation OR Pass-Testing OR Fail-Testing OR Fail-AuthenticationError OR Fail-FlapRecord OR Fail-IncompatibleForTesting OR Fail-MultipleTestErrors OR Fail-MultipleTestFail OR Fail-NoPower OR Fail-PartNotFound OR Fail-RetiredPart OR Pass-Repair OR Fail-PortFailure OR Fail-FRUFailureCombination OR Fail-FRUFailureFan OR Fail-FRUFailurePSU OR Fail-FRUFailureRAM OR Fail-TestingAfterMultipleRepairs OR Pass-FRUInstallCombination OR Pass-FRUInstallFan OR Pass-FRUInstallPSU OR Pass-FRUI<PERSON>allRAM OR Pass-FRURemoveReplaceCombination OR Pass-FRURemoveReplaceFan OR Pass-FRURemoveReplacePSU OR Pass-FRURemoveReplaceRAM OR Pass-InstallHeatSink OR Pass-RemoveReplaceHeatSink OR Pass-RMAWarrantyCheck OR Fail-Overheat OR Pass-InstallThermalPastePins OR Pass-RemoveReplaceThermalPastePins OR Fail-BrickUnit OR Fail-FlapReocrd OR Fail-RepeatOffender OR Fail-FA2Candidate OR Pass-Evaluation OR Pass-ReturnToVendorRMA-PARTOFASSEMBLY\" , Sanitization Flag = \"No\" , Attribute ID Contains \"Liquidation-Active\"  , Facility = \"All\" ", "created_date": "2025-06-05 18:56:10", "created_by": 733, "updated_date": null, "updated_by": null, "FacilityID": "all", "sub_disposition_id": null, "version_id": 71, "rule_id_text": "BRE_Rework_Prod_Copy_v5-6156", "AWSCustomerID": "all", "part_types": "All", "idCustomertype": "all", "MaterialType": "all"}, {"rule_id": 6157, "rule_name": "Recycle-4", "rule_description": "There is no higher recovery opportunity for this part, send to onsite processing for recycling", "priority": 51, "workflow_id": "5", "input_id": "124", "disposition_id": 27, "status": "Active", "rule_summary": "input = \"Pass-ChipHarvest\" , Attribute ID Contains \"Extract-HeatSinkForRepair\"  , Facility = \"All\" \"", "created_date": "2025-06-05 18:56:10", "created_by": 733, "updated_date": null, "updated_by": null, "FacilityID": "all", "sub_disposition_id": null, "version_id": 71, "rule_id_text": "BRE_Rework_Prod_Copy_v5-6157", "AWSCustomerID": "all", "part_types": "DONGLE", "idCustomertype": "all", "MaterialType": "all"}, {"rule_id": 6158, "rule_name": "Extraction-3", "rule_description": "Send to harvest/extraction for sub component removal", "priority": 52, "workflow_id": "5", "input_id": "125", "disposition_id": 24, "status": "Active", "rule_summary": "input = \"Fail-ChipHarvest\" , Demand Flag = \"No\" , Attribute ID Contains \"Extract-HeatSinkForRepair\"  , Facility = \"All\" ", "created_date": "2025-06-05 18:56:10", "created_by": 733, "updated_date": null, "updated_by": null, "FacilityID": "all", "sub_disposition_id": null, "version_id": 71, "rule_id_text": "BRE_Rework_Prod_Copy_v5-6158", "AWSCustomerID": "all", "part_types": "DONGLE", "idCustomertype": "all", "MaterialType": "all"}, {"rule_id": 6159, "rule_name": "Liquidation-2", "rule_description": "This part qualifies for liquidation when contractual blockers are removed, send to storage holding location for liquidation", "priority": 53, "workflow_id": "all", "input_id": "1,10,11,12,13,14,15,16,17,18,19,27,28,29,92,99,100,101,102,103,120,123,126,127,128,130", "disposition_id": 23, "status": "Active", "rule_summary": "input = \"Pass-Evaluation OR Pass-Testing OR Fail-Testing OR Fail-AuthenticationError OR Fail-FlapRecord OR Fail-IncompatibleForTesting OR Fail-MultipleTestErrors OR Fail-MultipleTestFail OR Fail-NoPower OR Fail-PartNotFound OR Fail-RetiredPart OR Fail-OutOfWarranty OR Fail-NotCoveredByWarranty OR Fail-MissingHostIdentification OR Fail-PortFailure OR Fail-FRUFailureCombination OR Fail-FRUFailureFan OR Fail-FRUFailurePSU OR Fail-FRUFailureRAM OR Fail-TestingAfterMultipleRepairs OR Fail-Overheat OR Fail-BrickUnit OR Fail-FlapReocrd OR Fail-RepeatOffender OR Fail-FA2Candidate OR Pass-Evaluation\" , Attribute ID Contains \"Liquidation-Delayed\" , Sanitization Flag = \"No\"  , Facility = \"All\" ", "created_date": "2025-06-05 18:56:10", "created_by": 733, "updated_date": null, "updated_by": null, "FacilityID": "all", "sub_disposition_id": null, "version_id": 71, "rule_id_text": "BRE_Rework_Prod_Copy_v5-6159", "AWSCustomerID": "all", "part_types": "All", "idCustomertype": "all", "MaterialType": "all"}, {"rule_id": 6160, "rule_name": "Harvest-1", "rule_description": "Send to harvest for sub component removal", "priority": 54, "workflow_id": "all", "input_id": "2,3,4,5,6,7,11,12,13,15,16,17,18,82,83,92,96,97,98,99,100,101,102,103,115,116,117,120,123,126,127,128,131,132,133,134,135,136,138,139,140,141,152,153", "disposition_id": 24, "status": "Active", "rule_summary": "input = \"Fail-DamagedBodyChassis OR Fail-DamagedChipsPins OR Fail-DamagedCords OR Fail-DamagedFixtures OR Fail-DamagedTestAccessPoint OR Fail-ExcessiveRust OR Fail-Testing OR Fail-AuthenticationError OR Fail-FlapRecord OR Fail-MultipleTestErrors OR Fail-MultipleTestFail OR Fail-NoPower OR Fail-PartNotFound OR Fail-Repair OR Fail-DamagedOrMissingHeatSink OR Fail-PortFailure OR Fail-FRUMissingCombination OR Fail-FRUMissingFan OR Fail-FRUMissingPSU OR Fail-FRUFailureCombination OR Fail-FRUFailureFan OR Fail-FRUFailurePSU OR Fail-FRUFailureRAM OR Fail-TestingAfterMultipleRepairs OR Fail-BeyondRepair OR Fail-InsufficientConsumables OR Fail-MultipleRepairs OR Fail-Overheat OR Fail-BrickUnit OR Fail-FlapReocrd OR Fail-RepeatOffender OR Fail-FA2Candidate OR Fail-DamagedBodyChassis OR Fail-DamagedChipsPins OR Fail-DamagedCords OR Fail-DamagedFixtures OR Fail-DamagedTestAccessPoint OR Fail-ExcessiveRust OR Fail-DamagedOrMissingHeatSink OR Fail-FRUMissingCombination OR Fail-FRUMissingFan OR Fail-FRUMissingPSU OR Fail-DamagedChips OR Fail-DamagedPins\" , Attribute ID Contains \"Failure_Analysis-FullTest\" , Attribute ID Contains \"Extract-HeatSinkForRepair\" , Demand Flag = \"Yes\"  , Facility = \"All\" ", "created_date": "2025-06-05 18:56:10", "created_by": 733, "updated_date": "2025-06-05 19:00:39", "updated_by": 733, "FacilityID": "all", "sub_disposition_id": null, "version_id": 71, "rule_id_text": "BRE_Rework_Prod_Copy_v5-6160", "AWSCustomerID": "all", "part_types": "DONGLE,GPU", "idCustomertype": "all", "MaterialType": "all"}, {"rule_id": 6161, "rule_name": "HarvestDongleChipForLiquidation", "rule_description": "Send to harvest for chip sub component removal", "priority": 55, "workflow_id": "5", "input_id": "124,30", "disposition_id": 35, "status": "Active", "rule_summary": "input = \"Pass-ChipHarvest OR Pass-Harvest\" , MPN = \"K2T-QB\"  , Facility = \"DUB210\"", "created_date": "2025-06-05 18:56:11", "created_by": 733, "updated_date": null, "updated_by": null, "FacilityID": "11", "sub_disposition_id": null, "version_id": 71, "rule_id_text": "BRE_Rework_Prod_Copy_v5-6161", "AWSCustomerID": "all", "part_types": "DONGLE", "idCustomertype": "all", "MaterialType": "all"}, {"rule_id": 6162, "rule_name": "HarvestDongleChipForLiquidation1", "rule_description": "Send to harvest for chip sub component removal", "priority": 56, "workflow_id": "5", "input_id": "30,124", "disposition_id": 35, "status": "Active", "rule_summary": "input = \"Pass-Harvest OR Pass-ChipHarvest\" , MPN = \"K2T-QB-TP1\"  , Facility = \"All\" ", "created_date": "2025-06-05 18:56:11", "created_by": 733, "updated_date": null, "updated_by": null, "FacilityID": "all", "sub_disposition_id": null, "version_id": 71, "rule_id_text": "BRE_Rework_Prod_Copy_v5-6162", "AWSCustomerID": "all", "part_types": "DONGLE", "idCustomertype": "all", "MaterialType": "all"}, {"rule_id": 6163, "rule_name": "HarvestDongleChipForLiquidation2", "rule_description": "HarvestDongleChipForLiquidation", "priority": 57, "workflow_id": "5", "input_id": "124,30", "disposition_id": 35, "status": "Active", "rule_summary": "input = \"Pass-ChipHarvest OR Pass-Harvest\" , MPN = \"K2T-QB-1TPM\"  , Facility = \"All\" \"", "created_date": "2025-06-05 18:56:11", "created_by": 733, "updated_date": null, "updated_by": null, "FacilityID": "all", "sub_disposition_id": null, "version_id": 71, "rule_id_text": "BRE_Rework_Prod_Copy_v5-6163", "AWSCustomerID": "all", "part_types": "DONGLE", "idCustomertype": "all", "MaterialType": "all"}, {"rule_id": 6164, "rule_name": "Recycle-3", "rule_description": "There is no higher recovery opportunity for this part, send to outbound shipping for recycling removal", "priority": 58, "workflow_id": "5", "input_id": "30,124", "disposition_id": 27, "status": "Active", "rule_summary": "input = \"Pass-Harvest OR Pass-ChipHarvest\" , , Facility = \"All\" ", "created_date": "2025-06-05 18:56:11", "created_by": 733, "updated_date": "2025-06-05 19:01:32", "updated_by": 733, "FacilityID": "all", "sub_disposition_id": null, "version_id": 71, "rule_id_text": "BRE_Rework_Prod_Copy_v5-6164", "AWSCustomerID": "all", "part_types": "All", "idCustomertype": "all", "MaterialType": "all"}, {"rule_id": 6165, "rule_name": "Recycle-NetworkGear", "rule_description": "Sanitized network gear (that require it), and non-sanitized network gear will route to HostDemanufacture for deman. This is a catchall in the event there is no other path for these parts", "priority": 59, "workflow_id": "all", "input_id": "1,2,3,4,5,6,7,8,12,13,25,27,28,29,83,96,97,98,99,100,101,102,123,126,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,149,152,153,155", "disposition_id": 94, "status": "Active", "rule_summary": "input = \"Pass-Evaluation OR Fail-DamagedBodyChassis OR Fail-DamagedChipsPins OR Fail-DamagedCords OR Fail-DamagedFixtures OR Fail-DamagedTestAccessPoint OR Fail-ExcessiveRust OR Fail-MissingCriticalPart OR Fail-AuthenticationError OR Fail-FlapRecord OR Fail-Sanitization OR Fail-OutOfWarranty OR Fail-NotCoveredByWarranty OR Fail-MissingHostIdentification OR Fail-DamagedOrMissingHeatSink OR Fail-FRUMissingCombination OR Fail-FRUMissingFan OR Fail-FRUMissingPSU OR Fail-FRUFailureCombination OR Fail-FRUFailureFan OR Fail-FRUFailurePSU OR Fail-FRUFailureRAM OR Fail-BrickUnit OR Fail-FlapReocrd OR Fail-FA2Candidate OR Fail-OutdatedHeatSinkOrPins OR Pass-Evaluation OR Fail-DamagedBodyChassis OR Fail-DamagedChipsPins OR Fail-DamagedCords OR Fail-DamagedFixtures OR Fail-DamagedTestAccessPoint OR Fail-ExcessiveRust OR Fail-MissingCriticalPart OR Fail-DamagedOrMissingHeatSink OR Fail-FRUMissingCombination OR Fail-FRUMissingFan OR Fail-FRUMissingPSU OR Fail-OutdatedHeatSinkOrPins OR Fail-ImproperPackaging OR Fail-DamagedChips OR Fail-DamagedPins OR Fail-NewMediaFail\" , , Facility = \"All\" ", "created_date": "2025-06-05 18:56:11", "created_by": 733, "updated_date": null, "updated_by": null, "FacilityID": "all", "sub_disposition_id": null, "version_id": 71, "rule_id_text": "BRE_Rework_Prod_Copy_v5-6165", "AWSCustomerID": "all", "part_types": "AWS DWDM,AWS NETWORK ACCESSORY,AWS ROUTER,AWS SWITCH,CHASSIS", "idCustomertype": "all", "MaterialType": "all"}, {"rule_id": 6166, "rule_name": "APRecycle", "rule_description": "Annapurna Labs rule to prevent standard recycle and force recycle processed", "priority": 60, "workflow_id": "all", "input_id": "2,3,4,5,6,11,12,13,15,16,17,18,82,83,92,93,94,95,99,100,101,102,103,104,115,116,117,120,123,125,126,127,128,131,132,133,134,138,144,146,148,152,153,13", "disposition_id": 27, "status": "Active", "rule_summary": "input = \"Fail-DamagedBodyChassis OR Fail-DamagedChipsPins OR Fail-DamagedCords OR Fail-DamagedFixtures OR Fail-DamagedTestAccessPoint OR Fail-Testing OR Fail-AuthenticationError OR Fail-FlapRecord OR Fail-MultipleTestErrors OR Fail-MultipleTestFail OR Fail-NoPower OR Fail-PartNotFound OR Fail-Repair OR Fail-DamagedOrMissingHeatSink OR Fail-PortFailure OR Fail-NoEligibleSubComponents OR Fail-NoEligibleFRU OR Fail-Harvest OR Fail-FRUFailureCombination OR Fail-FRUFailureFan OR Fail-FRUFailurePSU OR Fail-FRUFailureRAM OR Fail-TestingAfterMultipleRepairs OR Fail-RetiredFRU OR Fail-BeyondRepair OR Fail-InsufficientConsumables OR Fail-MultipleRepairs OR Fail-Overheat OR Fail-BrickUnit OR Fail-ChipHarvest OR Fail-FlapReocrd OR Fail-RepeatOffender OR Fail-FA2Candidate OR Fail-DamagedBodyChassis OR Fail-DamagedChipsPins OR Fail-DamagedCords OR Fail-DamagedFixtures OR Fail-DamagedOrMissingHeatSink OR Fail-ChipHarvest OR Fail-Harvest OR Fail-ChipRecovery OR Fail-DamagedChips OR Fail-DamagedPins OR Fail-DamagedTestAccessPoint OR Fail-ExcessiveRust OR Fail-ExcessiveRust OR Fail-FRUMissingCombination OR Fail-FRUMissingCombination OR Fail-FRUMissingFan OR Fail-FRUMissingFan OR Fail-FRUMissingPSU OR Fail-FRUMissingPSU OR Fail-ImproperPackaging OR Fail-MissingCriticalPart OR Fail-MissingCriticalPart OR Fail-OutdatedHeatSinkOrPins OR Fail-OutdatedHeatSinkOrPins\" , Manufacturer = \"1636\"  , Facility = \"All\" ", "created_date": "2025-06-05 18:56:11", "created_by": 733, "updated_date": null, "updated_by": null, "FacilityID": "all", "sub_disposition_id": null, "version_id": 71, "rule_id_text": "BRE_Rework_Prod_Copy_v5-6166", "AWSCustomerID": "all", "part_types": "GPU", "idCustomertype": "all", "MaterialType": "all"}, {"rule_id": 6167, "rule_name": "Recycle-1", "rule_description": "There is no higher recovery opportunity for this part, send to outbound shipping for recycling removal", "priority": 61, "workflow_id": "all", "input_id": "1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,27,28,29,30,38,39,40,72,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,151,152,153,154,155,156", "disposition_id": 10, "status": "Active", "rule_summary": "input = \"Pass-Evaluation OR Fail-DamagedBodyChassis OR Fail-DamagedChipsPins OR Fail-DamagedCords OR Fail-DamagedFixtures OR Fail-DamagedTestAccessPoint OR Fail-ExcessiveRust OR Fail-MissingCriticalPart OR Sideline-ProblemSolve OR Pass-Testing OR Fail-Testing OR Fail-AuthenticationError OR Fail-FlapRecord OR Fail-IncompatibleForTesting OR Fail-MultipleTestErrors OR Fail-MultipleTestFail OR Fail-NoPower OR Fail-PartNotFound OR Fail-RetiredPart OR Retest-FirstTestError OR Retest-SecondTestError OR Retest-FirstTestFail OR Retest-SecondTestFail OR Pass-Sanitization OR Fail-Sanitization OR Fail-OutOfWarranty OR Fail-NotCoveredByWarranty OR Fail-MissingHostIdentification OR Pass-Harvest OR Pass Removal OR testwfI OR ds OR Fail-FireFightingOctopus OR Pass-Repair OR Fail-Repair OR Fail-DamagedOrMissingHeatSink OR Fail-MissingPSUFRU OR Fail-MissingFanFRU OR Fail-MissingCombinationFRU OR Fail-PSUFRUFailure OR Fail-FanFRUFailure OR Fail-RAMFRUFailure OR Fail-CombinationFRUFailure OR Fail-BrickedUnit OR Fail-PortFailure OR Fail-NoEligibleSubComponents OR Fail-NoEligibleFRU OR Fail-Harvest OR Fail-FRUMissingCombination OR Fail-FRUMissingFan OR Fail-FRUMissingPSU OR Fail-FRUFailureCombination OR Fail-FRUFailureFan OR Fail-FRUFailurePSU OR Fail-FRUFailureRAM OR Fail-TestingAfterMultipleRepairs OR Fail-RetiredFRU OR Pass-FRUInstallCombination OR Pass-FRUInstallFan OR Pass-FRUInstallPSU OR Pass-FRUInstallRAM OR Pass-FRURemoveReplaceCombination OR Pass-FRURemoveReplaceFan OR Pass-FRURemoveReplacePSU OR Pass-FRURemoveReplaceRAM OR Pass-InstallHeatSink OR Pass-RemoveReplaceHeatSink OR Fail-BeyondRepair OR Fail-InsufficientConsumables OR Fail-MultipleRepairs OR Pass-RMAWarrantyCheck OR Fail-Overheat OR Pass-InstallThermalPastePins OR Pass-RemoveReplaceThermalPastePins OR Fail-BrickUnit OR Pass-ChipHarvest OR Fail-ChipHarvest OR Fail-FlapReocrd OR Fail-RepeatOffender OR Fail-FA2Candidate OR Fail-OutdatedHeatSinkOrPins OR Pass-Evaluation OR Fail-DamagedBodyChassis OR Fail-DamagedChipsPins OR Fail-DamagedCords OR Fail-DamagedFixtures OR Fail-DamagedTestAccessPoint OR Fail-ExcessiveRust OR Fail-MissingCriticalPart OR Fail-DamagedOrMissingHeatSink OR Fail-FRUMissingCombination OR Fail-FRUMissingFan OR Fail-FRUMissingPSU OR Fail-OutdatedHeatSinkOrPins OR Pass-ChipHarvest OR Fail-ChipHarvest OR Pass-Harvest OR Fail-Harvest OR Pass-ChipRecovery OR Fail-ChipRecovery OR Fail-ImproperPackaging OR Fail-DamagedChassisOrEnclosure OR Fail-DamagedChips OR Fail-DamagedPins OR Fail-DamagedOrMissingHeatSink OR Fail-NewMediaFail OR Pass-ReturnToVendorRMA-PARTOFASSEMBLY\" , Sanitization Flag = \"No\"  , Facility = \"All\" ", "created_date": "2025-06-05 18:56:11", "created_by": 733, "updated_date": null, "updated_by": null, "FacilityID": "all", "sub_disposition_id": null, "version_id": 71, "rule_id_text": "BRE_Rework_Prod_Copy_v5-6167", "AWSCustomerID": "all", "part_types": "All", "idCustomertype": "all", "MaterialType": "all"}, {"rule_id": 6168, "rule_name": "Recycle-2", "rule_description": "There is no higher recovery opportunity for this part, send to outbound shipping for recycling removal", "priority": 62, "workflow_id": "all", "input_id": "1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,27,28,29,30,38,39,40,72,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,151,152,153,154,155,156", "disposition_id": 27, "status": "Active", "rule_summary": "input = \"Pass-Evaluation OR Fail-DamagedBodyChassis OR Fail-DamagedChipsPins OR Fail-DamagedCords OR Fail-DamagedFixtures OR Fail-DamagedTestAccessPoint OR Fail-ExcessiveRust OR Fail-MissingCriticalPart OR Sideline-ProblemSolve OR Pass-Testing OR Fail-Testing OR Fail-AuthenticationError OR Fail-FlapRecord OR Fail-IncompatibleForTesting OR Fail-MultipleTestErrors OR Fail-MultipleTestFail OR Fail-NoPower OR Fail-PartNotFound OR Fail-RetiredPart OR Retest-FirstTestError OR Retest-SecondTestError OR Retest-FirstTestFail OR Retest-SecondTestFail OR Pass-Sanitization OR Fail-Sanitization OR Fail-OutOfWarranty OR Fail-NotCoveredByWarranty OR Fail-MissingHostIdentification OR Pass-Harvest OR Pass Removal OR testwfI OR ds OR Fail-FireFightingOctopus OR Pass-Repair OR Fail-Repair OR Fail-DamagedOrMissingHeatSink OR Fail-MissingPSUFRU OR Fail-MissingFanFRU OR Fail-MissingCombinationFRU OR Fail-PSUFRUFailure OR Fail-FanFRUFailure OR Fail-RAMFRUFailure OR Fail-CombinationFRUFailure OR Fail-BrickedUnit OR Fail-PortFailure OR Fail-NoEligibleSubComponents OR Fail-NoEligibleFRU OR Fail-Harvest OR Fail-FRUMissingCombination OR Fail-FRUMissingFan OR Fail-FRUMissingPSU OR Fail-FRUFailureCombination OR Fail-FRUFailureFan OR Fail-FRUFailurePSU OR Fail-FRUFailureRAM OR Fail-TestingAfterMultipleRepairs OR Fail-RetiredFRU OR Pass-FRUInstallCombination OR Pass-FRUInstallFan OR Pass-FRUInstallPSU OR Pass-FRUInstallRAM OR Pass-FRURemoveReplaceCombination OR Pass-FRURemoveReplaceFan OR Pass-FRURemoveReplacePSU OR Pass-FRURemoveReplaceRAM OR Pass-InstallHeatSink OR Pass-RemoveReplaceHeatSink OR Fail-BeyondRepair OR Fail-InsufficientConsumables OR Fail-MultipleRepairs OR Pass-RMAWarrantyCheck OR Fail-Overheat OR Pass-InstallThermalPastePins OR Pass-RemoveReplaceThermalPastePins OR Fail-BrickUnit OR Pass-ChipHarvest OR Fail-ChipHarvest OR Fail-FlapReocrd OR Fail-RepeatOffender OR Fail-FA2Candidate OR Fail-OutdatedHeatSinkOrPins OR Pass-Evaluation OR Fail-DamagedBodyChassis OR Fail-DamagedChipsPins OR Fail-DamagedCords OR Fail-DamagedFixtures OR Fail-DamagedTestAccessPoint OR Fail-ExcessiveRust OR Fail-MissingCriticalPart OR Fail-DamagedOrMissingHeatSink OR Fail-FRUMissingCombination OR Fail-FRUMissingFan OR Fail-FRUMissingPSU OR Fail-OutdatedHeatSinkOrPins OR Pass-ChipHarvest OR Fail-ChipHarvest OR Pass-Harvest OR Fail-Harvest OR Pass-ChipRecovery OR Fail-ChipRecovery OR Fail-ImproperPackaging OR Fail-DamagedChassisOrEnclosure OR Fail-DamagedChips OR Fail-DamagedPins OR Fail-DamagedOrMissingHeatSink OR Fail-NewMediaFail OR Pass-ReturnToVendorRMA-PARTOFASSEMBLY\" , , Facility = \"All\" ", "created_date": "2025-06-05 18:56:11", "created_by": 733, "updated_date": null, "updated_by": null, "FacilityID": "all", "sub_disposition_id": null, "version_id": 71, "rule_id_text": "BRE_Rework_Prod_Copy_v5-6168", "AWSCustomerID": "all", "part_types": "All", "idCustomertype": "all", "MaterialType": "all"}, {"rule_id": 6169, "rule_name": "PreRepairToRepair", "rule_description": "This part should be repaired before it is tested to determine suitability in the fleet, send to failure analysis testing", "priority": 32, "workflow_id": "7", "input_id": "114", "disposition_id": 19, "status": "Active", "rule_summary": "input = \"Pass-RemoveReplaceHeatSink\" , Demand Flag = \"Yes\"  , Facility = \"All\" ", "created_date": "2025-06-05 18:56:11", "created_by": 733, "updated_date": null, "updated_by": null, "FacilityID": "all", "sub_disposition_id": null, "version_id": 71, "rule_id_text": "BRE_Rework_Prod_Copy_v5-6169", "AWSCustomerID": "all", "part_types": "All", "idCustomertype": "all", "MaterialType": "all"}, {"rule_id": 6170, "rule_name": "FailureAnalysis-4", "rule_description": "This repaired part should be tested to determine suitability in the fleet, send to failure analysis testing", "priority": 33, "workflow_id": "7", "input_id": "113,114", "disposition_id": 2, "status": "Active", "rule_summary": "input = \"Pass-InstallHeatSink OR Pass-RemoveReplaceHeatSink\" , Attribute ID Contains \"Repair-HeatSinkReplacement\" , Demand Flag = \"Yes\"  , Facility = \"All\" ", "created_date": "2025-06-05 18:56:11", "created_by": 733, "updated_date": null, "updated_by": null, "FacilityID": "all", "sub_disposition_id": null, "version_id": 71, "rule_id_text": "BRE_Rework_Prod_Copy_v5-6170", "AWSCustomerID": "all", "part_types": "DONGLE", "idCustomertype": "all", "MaterialType": "all"}, {"rule_id": 6171, "rule_name": "ReturntoVendor-RMADirect", "rule_description": "This path will direct part to OEM vendor", "priority": 44, "workflow_id": "4", "input_id": "118,156", "disposition_id": 100, "status": "Active", "rule_summary": "input = \"Pass-RMAWarrantyCheck OR Pass-ReturnToVendorRMA-PARTOFASSEMBLY\" , RMA Direct = \"Yes\"  , Facility = \"All\" ", "created_date": "2025-06-05 18:56:11", "created_by": 733, "updated_date": null, "updated_by": null, "FacilityID": "all", "sub_disposition_id": null, "version_id": 71, "rule_id_text": "BRE_Rework_Prod_Copy_v5-6171", "AWSCustomerID": "all", "part_types": "All", "idCustomertype": "all", "MaterialType": "all"}]