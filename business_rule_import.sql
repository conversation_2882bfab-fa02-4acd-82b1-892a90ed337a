-- MySQL INSERT statements for awsprod.business_rule table
-- Generated from bre_from_staging.json (without disposition_id and sub_disposition_id)

INSERT INTO awsprod.business_rule (
    rule_name, rule_description, priority, workflow_id, status,
    rule_summary, created_date, created_by, FacilityID,
    version_id, rule_id_text, AWSCustomerID, part_types, idCustomertype, MaterialType
) VALUES (
    'Compromised Parts - Direct to Recycle Processed',
    'Arrived at RRL with At Risk Parts from an AWS Incident',
    1,
    '10',
    'Active',
    'input = \"Fail-DamagedBodyChassis OR Fail-DamagedChipsPins OR Fail-DamagedCords OR Fail-DamagedFixtures OR Fail-DamagedOrMissingHeatSink OR Fail-DamagedTestAccessPoint OR Fail-ExcessiveRust OR Fail-FRUMissingCombination OR Fail-FRUMissingFan OR Fail-FRUMissingPSU OR Fail-ImproperPackaging OR Fail-MissingCriticalPart OR Fail-OutdatedHeatSinkOrPins OR Pass-Evaluation\" , , Facility = \"All\" \"',
    NOW(),
    1075,
    'all',
    83,
    'BRE_Rework_Prod_Copy_v5-6109',
    'all',
    'All',
    '17',
    'all'
);

INSERT INTO awsprod.business_rule (
    rule_name, rule_description, priority, workflow_id, status,
    rule_summary, created_date, created_by, FacilityID,
    version_id, rule_id_text, AWSCustomerID, part_types, idCustomertype, MaterialType
) VALUES (
    'SidelineRecoveryProject',
    'A stakeholder has targeted this part for additional research, sideline for a recovery project',
    2,
    '10',
    'Active',
    'input = \"Pass-Evaluation OR Fail-DamagedBodyChassis OR Fail-DamagedChipsPins OR Fail-DamagedCords OR Fail-DamagedFixtures OR Fail-DamagedTestAccessPoint OR Fail-ExcessiveRust OR Fail-MissingCriticalPart OR Fail-DamagedOrMissingHeatSink OR Fail-FRUMissingCombination OR Fail-FRUMissingFan OR Fail-FRUMissingPSU OR Fail-OutdatedHeatSinkOrPins OR Fail-ImproperPackaging\" , Recovery Project = \"Yes\"  , Facility = \"All\" ',
    NOW(),
    1075,
    'all',
    83,
    'BRE_Rework_Prod_Copy_v5-6110',
    'all',
    'All',
    'all',
    'all'
);

INSERT INTO awsprod.business_rule (
    rule_name, rule_description, priority, workflow_id, status,
    rule_summary, created_date, created_by, FacilityID,
    version_id, rule_id_text, AWSCustomerID, part_types, idCustomertype, MaterialType
) VALUES (
    'SidelineMBGraviton',
    'Must sideline all motherboards that have a graviton CPU. For extraction later',
    3,
    '10',
    'Active',
    'input = \"Pass-Evaluation OR Fail-DamagedBodyChassis OR Fail-DamagedChipsPins OR Fail-DamagedCords OR Fail-DamagedFixtures OR Fail-DamagedTestAccessPoint OR Fail-ExcessiveRust OR Fail-MissingCriticalPart OR Fail-DamagedOrMissingHeatSink OR Fail-FRUMissingCombination OR Fail-FRUMissingFan OR Fail-FRUMissingPSU OR Fail-OutdatedHeatSinkOrPins OR Fail-ImproperPackaging\" , Attribute ID Contains \"Extract-CPU\"  , Facility = \"All\" ',
    NOW(),
    1075,
    'all',
    83,
    'BRE_Rework_Prod_Copy_v5-6111',
    'all',
    'MOTHERBOARD',
    'all',
    'all'
);

INSERT INTO awsprod.business_rule (
    rule_name, rule_description, priority, workflow_id, status,
    rule_summary, created_date, created_by, FacilityID,
    version_id, rule_id_text, AWSCustomerID, part_types, idCustomertype, MaterialType
) VALUES (
    'SidelineSpecialHandling',
    'A stakeholder has targeted this part for additional research, sideline for special handling',
    4,
    '10',
    'Active',
    'input = \"Pass-Evaluation OR Fail-DamagedBodyChassis OR Fail-DamagedChipsPins OR Fail-DamagedCords OR Fail-DamagedFixtures OR Fail-DamagedTestAccessPoint OR Fail-ExcessiveRust OR Fail-MissingCriticalPart OR Fail-DamagedOrMissingHeatSink OR Fail-FRUMissingCombination OR Fail-FRUMissingFan OR Fail-FRUMissingPSU OR Fail-OutdatedHeatSinkOrPins OR Fail-ImproperPackaging\" , Special Handling = \"Yes\"  , Facility = \"All\" ',
    NOW(),
    1075,
    'all',
    83,
    'BRE_Rework_Prod_Copy_v5-6112',
    'all',
    'All',
    'all',
    'all'
);

INSERT INTO awsprod.business_rule (
    rule_name, rule_description, priority, workflow_id, status,
    rule_summary, created_date, created_by, FacilityID,
    version_id, rule_id_text, AWSCustomerID, part_types, idCustomertype, MaterialType
) VALUES (
    'SidelineRMADirect',
    'These parts should be returned to OEM vendor upon receipt',
    5,
    '10',
    'Active',
    'input = \"Pass-Evaluation OR Fail-DamagedBodyChassis OR Fail-DamagedChipsPins OR Fail-DamagedCords OR Fail-DamagedFixtures OR Fail-DamagedTestAccessPoint OR Fail-ExcessiveRust OR Fail-MissingCriticalPart OR Fail-DamagedOrMissingHeatSink OR Fail-FRUMissingCombination OR Fail-FRUMissingFan OR Fail-FRUMissingPSU OR Fail-OutdatedHeatSinkOrPins OR Fail-ImproperPackaging\" , RMA Direct = \"Yes\"  , Facility = \"All\" ',
    NOW(),
    1075,
    'all',
    83,
    'BRE_Rework_Prod_Copy_v5-6113',
    'all',
    'All',
    'all',
    'all'
);

INSERT INTO awsprod.business_rule (
    rule_name, rule_description, priority, workflow_id, status,
    rule_summary, created_date, created_by, FacilityID,
    version_id, rule_id_text, AWSCustomerID, part_types, idCustomertype, MaterialType
) VALUES (
    'RecycleP-WEEE',
    'Any part with sanitization coming from the DC-WEEE source type needs to be recycle processed',
    6,
    '10',
    'Active',
    'input = \"Fail-DamagedBodyChassis OR Fail-DamagedChipsPins OR Fail-DamagedCords OR Fail-DamagedFixtures OR Fail-DamagedOrMissingHeatSink OR Fail-DamagedTestAccessPoint OR Fail-ExcessiveRust OR Fail-FRUMissingCombination OR Fail-FRUMissingFan OR Fail-FRUMissingPSU OR Fail-ImproperPackaging OR Fail-MissingCriticalPart OR Fail-OutdatedHeatSinkOrPins OR Pass-Evaluation\" , Sanitization Flag = \"Yes\"  , Facility = \"All\" \"',
    NOW(),
    1075,
    'all',
    83,
    'BRE_Rework_Prod_Copy_v5-6114',
    'all',
    'All',
    '16',
    'all'
);

INSERT INTO awsprod.business_rule (
    rule_name, rule_description, priority, workflow_id, status,
    rule_summary, created_date, created_by, FacilityID,
    version_id, rule_id_text, AWSCustomerID, part_types, idCustomertype, MaterialType
) VALUES (
    'LiquidationA-WEEE',
    'Any part coming from DC-WEE source type, and with liquidationActive attribute, will be pathed to Liquidation active',
    7,
    '10',
    'Active',
    'input = \"Fail-DamagedBodyChassis OR Fail-DamagedChipsPins OR Fail-DamagedCords OR Fail-DamagedFixtures OR Fail-DamagedOrMissingHeatSink OR Fail-DamagedTestAccessPoint OR Fail-ExcessiveRust OR Fail-FRUMissingCombination OR Fail-FRUMissingFan OR Fail-FRUMissingPSU OR Fail-ImproperPackaging OR Fail-MissingCriticalPart OR Fail-OutdatedHeatSinkOrPins OR Pass-Evaluation\" , Attribute ID Contains \"Liquidation-Active\"  , Facility = \"All\" \"',
    NOW(),
    1075,
    'all',
    83,
    'BRE_Rework_Prod_Copy_v5-6115',
    'all',
    'All',
    '16',
    'all'
);

INSERT INTO awsprod.business_rule (
    rule_name, rule_description, priority, workflow_id, status,
    rule_summary, created_date, created_by, FacilityID,
    version_id, rule_id_text, AWSCustomerID, part_types, idCustomertype, MaterialType
) VALUES (
    'RecycleS-WEEE',
    'Catchall for any other parts coming from DC-WEEE source type that aren\'t RP or LiqA',
    8,
    '10',
    'Active',
    'input = \"Pass-Evaluation OR Fail-DamagedBodyChassis OR Fail-DamagedChipsPins OR Fail-DamagedCords OR Fail-DamagedFixtures OR Fail-DamagedOrMissingHeatSink OR Fail-DamagedTestAccessPoint OR Fail-ExcessiveRust OR Fail-FRUMissingCombination OR Fail-FRUMissingFan OR Fail-FRUMissingPSU OR Fail-ImproperPackaging OR Fail-MissingCriticalPart OR Fail-OutdatedHeatSinkOrPins\" , , Facility = \"All\" ',
    NOW(),
    1075,
    'all',
    83,
    'BRE_Rework_Prod_Copy_v5-6116',
    'all',
    'All',
    '16',
    'all'
);

INSERT INTO awsprod.business_rule (
    rule_name, rule_description, priority, workflow_id, status,
    rule_summary, created_date, created_by, FacilityID,
    version_id, rule_id_text, AWSCustomerID, part_types, idCustomertype, MaterialType
) VALUES (
    'HighPriorityFA',
    'This parts has been identified as Critical or High Priority and should be tested first based on demand.',
    9,
    '10',
    'Active',
    'input = \"Pass-Evaluation\" , Priority Type = \"Critical\" , First Arrival = \"Yes\" , Attribute ID Contains \"Failure_Analysis-FullTest\"  , Facility = \"All\" ',
    NOW(),
    1075,
    'all',
    83,
    'BRE_Rework_Prod_Copy_v5-6117',
    'all',
    'AWS OPTICAL MODULE,RAM',
    'all',
    'all'
);

INSERT INTO awsprod.business_rule (
    rule_name, rule_description, priority, workflow_id, status,
    rule_summary, created_date, created_by, FacilityID,
    version_id, rule_id_text, AWSCustomerID, part_types, idCustomertype, MaterialType
) VALUES (
    'Deman_DongleFA',
    'Part qualifies for FA, route to FA Lab from demanufacture operation',
    10,
    '10',
    'Active',
    'input = \"Pass-Evaluation\" , Attribute ID Contains \"Failure_Analysis-FullTest\" , Fleet Risk = \"No\" , Demand Flag = \"Yes\"  , Facility = \"All\" ',
    NOW(),
    1075,
    'all',
    83,
    'BRE_Rework_Prod_Copy_v5-6118',
    'all',
    'DONGLE',
    '2',
    'all'
);

INSERT INTO awsprod.business_rule (
    rule_name, rule_description, priority, workflow_id, status,
    rule_summary, created_date, created_by, FacilityID,
    version_id, rule_id_text, AWSCustomerID, part_types, idCustomertype, MaterialType
) VALUES (
    'Deman_SwitchFA',
    'Part qualifies for FA, route to FA Lab from demanufacture operation',
    11,
    '10',
    'Active',
    'input = \"Pass-Evaluation\" , First Arrival = \"Yes\" , Fleet Risk = \"No\" , Demand Flag = \"Yes\" , Attribute ID Contains \"Failure_Analysis-FullTest\"  , Facility = \"All\" \"',
    NOW(),
    1075,
    'all',
    83,
    'BRE_Rework_Prod_Copy_v5-6119',
    'all',
    'AWS SWITCH',
    '2',
    'all'
);

INSERT INTO awsprod.business_rule (
    rule_name, rule_description, priority, workflow_id, status,
    rule_summary, created_date, created_by, FacilityID,
    version_id, rule_id_text, AWSCustomerID, part_types, idCustomertype, MaterialType
) VALUES (
    'Deman_CPU_Liquidation',
    'Part qualifies for liquidation, route to liquidation from demanufacture operation',
    12,
    '10',
    'Active',
    'input = \"Pass-Evaluation\" , Sanitization Flag = \"No\" , Attribute ID Contains \"Liquidation-Active\"  , Facility = \"All\" ',
    NOW(),
    1075,
    'all',
    83,
    'BRE_Rework_Prod_Copy_v5-6120',
    'all',
    'CPU',
    '2',
    'all'
);

INSERT INTO awsprod.business_rule (
    rule_name, rule_description, priority, workflow_id, status,
    rule_summary, created_date, created_by, FacilityID,
    version_id, rule_id_text, AWSCustomerID, part_types, idCustomertype, MaterialType
) VALUES (
    'Deman_RAM_Liquidation',
    'Part qualifies for liquidation, route to liquidation from demanufacture operation',
    13,
    '10',
    'Active',
    'input = \"Pass-Evaluation\" , Sanitization Flag = \"No\" , Attribute ID Contains \"Liquidation-Active\"  , Facility = \"All\" ',
    NOW(),
    1075,
    'all',
    83,
    'BRE_Rework_Prod_Copy_v5-6121',
    'all',
    'RAM',
    '2',
    'all'
);

INSERT INTO awsprod.business_rule (
    rule_name, rule_description, priority, workflow_id, status,
    rule_summary, created_date, created_by, FacilityID,
    version_id, rule_id_text, AWSCustomerID, part_types, idCustomertype, MaterialType
) VALUES (
    'Deman_Switch_Recovery',
    'This part requires disassembly, sideline for parts recovery',
    14,
    '10',
    'Active',
    'input = \"Pass-Evaluation OR Fail-DamagedBodyChassis OR Fail-DamagedChipsPins OR Fail-DamagedCords OR Fail-DamagedFixtures OR Fail-DamagedTestAccessPoint OR Fail-ExcessiveRust OR Fail-MissingCriticalPart OR Fail-DamagedOrMissingHeatSink OR Fail-FRUMissingCombination OR Fail-FRUMissingFan OR Fail-FRUMissingPSU OR Fail-OutdatedHeatSinkOrPins OR Fail-ImproperPackaging\" , , Facility = \"All\" ',
    NOW(),
    1075,
    'all',
    83,
    'BRE_Rework_Prod_Copy_v5-6122',
    'all',
    'AWS SWITCH',
    '2',
    'all'
);

INSERT INTO awsprod.business_rule (
    rule_name, rule_description, priority, workflow_id, status,
    rule_summary, created_date, created_by, FacilityID,
    version_id, rule_id_text, AWSCustomerID, part_types, idCustomertype, MaterialType
) VALUES (
    'Deman_Motherboard_Recycle',
    'There is no higher recovery opportunity for this part, send to outbound shipping for recycling removal',
    15,
    '10',
    'Active',
    'input = \"Pass-Evaluation OR Fail-DamagedBodyChassis OR Fail-DamagedChipsPins OR Fail-DamagedCords OR Fail-DamagedFixtures OR Fail-DamagedTestAccessPoint OR Fail-ExcessiveRust OR Fail-MissingCriticalPart OR Fail-DamagedOrMissingHeatSink OR Fail-FRUMissingCombination OR Fail-FRUMissingFan OR Fail-FRUMissingPSU OR Fail-OutdatedHeatSinkOrPins OR Fail-ImproperPackaging\" , Sanitization Flag = \"No\"  , Facility = \"All\" ',
    NOW(),
    1075,
    'all',
    83,
    'BRE_Rework_Prod_Copy_v5-6123',
    'all',
    'MOTHERBOARD',
    '2',
    'all'
);

INSERT INTO awsprod.business_rule (
    rule_name, rule_description, priority, workflow_id, status,
    rule_summary, created_date, created_by, FacilityID,
    version_id, rule_id_text, AWSCustomerID, part_types, idCustomertype, MaterialType
) VALUES (
    'Deman_CPU_Recycle',
    'There is no higher recovery opportunity for this part, route to recycling removal',
    16,
    '10',
    'Active',
    'input = \"Pass-Evaluation\" , Sanitization Flag = \"No\"  , Facility = \"All\" \"',
    NOW(),
    1075,
    'all',
    83,
    'BRE_Rework_Prod_Copy_v5-6124',
    'all',
    'CPU',
    '2',
    'all'
);

INSERT INTO awsprod.business_rule (
    rule_name, rule_description, priority, workflow_id, status,
    rule_summary, created_date, created_by, FacilityID,
    version_id, rule_id_text, AWSCustomerID, part_types, idCustomertype, MaterialType
) VALUES (
    'Deman_RAM_Investigation',
    'When a RAM part type has a Sanitization = \"Yes\" attribute, we want to ensure that they truly need to be destroyed before losing valuable products.',
    17,
    '10',
    'Active',
    'input = \"Pass-Evaluation\" , Sanitization Flag = \"Yes\"  , Facility = \"All\" ',
    NOW(),
    1075,
    'all',
    83,
    'BRE_Rework_Prod_Copy_v5-6125',
    'all',
    'RAM',
    '2',
    'all'
);

INSERT INTO awsprod.business_rule (
    rule_name, rule_description, priority, workflow_id, status,
    rule_summary, created_date, created_by, FacilityID,
    version_id, rule_id_text, AWSCustomerID, part_types, idCustomertype, MaterialType
) VALUES (
    'Deman_CPU_Destruction',
    'There is no higher recovery opportunity for this part, consolidate for local destructive sanitization',
    18,
    '10',
    'Active',
    'input = \"Fail-DamagedBodyChassis OR Fail-DamagedChipsPins OR Fail-DamagedCords OR Fail-DamagedFixtures OR Fail-DamagedOrMissingHeatSink OR Fail-DamagedTestAccessPoint OR Fail-ExcessiveRust OR Fail-FRUMissingCombination OR Fail-FRUMissingFan OR Fail-FRUMissingPSU OR Fail-ImproperPackaging OR Fail-MissingCriticalPart OR Fail-OutdatedHeatSinkOrPins\" , Sanitization Flag = \"Yes\"  , Facility = \"All\" \"',
    NOW(),
    1075,
    'all',
    83,
    'BRE_Rework_Prod_Copy_v5-6126',
    'all',
    'CPU',
    '2',
    'all'
);

INSERT INTO awsprod.business_rule (
    rule_name, rule_description, priority, workflow_id, status,
    rule_summary, created_date, created_by, FacilityID,
    version_id, rule_id_text, AWSCustomerID, part_types, idCustomertype, MaterialType
) VALUES (
    'Deman_RAM_LiquidationDamaged',
    'We will be repairing damaged DIMMs for liquidation, route these parts to the appropriate repair process',
    19,
    '10',
    'Active',
    'input = \"Fail-DamagedBodyChassis OR Fail-DamagedChipsPins OR Fail-DamagedCords OR Fail-DamagedFixtures OR Fail-DamagedTestAccessPoint OR Fail-ExcessiveRust OR Fail-MissingCriticalPart OR Fail-DamagedOrMissingHeatSink OR Fail-FRUMissingCombination OR Fail-FRUMissingFan OR Fail-FRUMissingPSU OR Fail-OutdatedHeatSinkOrPins OR Fail-ImproperPackaging\" , , Facility = \"All\" ',
    NOW(),
    1075,
    'all',
    83,
    'BRE_Rework_Prod_Copy_v5-6127',
    'all',
    'RAM',
    '2',
    'all'
);

INSERT INTO awsprod.business_rule (
    rule_name, rule_description, priority, workflow_id, status,
    rule_summary, created_date, created_by, FacilityID,
    version_id, rule_id_text, AWSCustomerID, part_types, idCustomertype, MaterialType
) VALUES (
    'FailureAnalysis-6',
    'This repaired part should be tested to determine suitability in the fleet, send to failure analysis testing',
    34,
    '7',
    'Active',
    'input = \"Pass-RemoveReplaceThermalPastePins\" , Attribute ID Contains \"Repair-HeatSinkReplacement\" , Demand Flag = \"Yes\"  , Facility = \"All\" \"',
    NOW(),
    1075,
    'all',
    83,
    'BRE_Rework_Prod_Copy_v5-6128',
    'all',
    'DONGLE',
    'all',
    'all'
);

INSERT INTO awsprod.business_rule (
    rule_name, rule_description, priority, workflow_id, status,
    rule_summary, created_date, created_by, FacilityID,
    version_id, rule_id_text, AWSCustomerID, part_types, idCustomertype, MaterialType
) VALUES (
    'FailureAnalysis-11',
    'This repaired part should be tested to determine suitability in the fleet, send to failure analysis testing',
    35,
    '7',
    'Active',
    'input = \"Pass-Repair\" , Demand Flag = \"Yes\"  , Facility = \"All\" \"',
    NOW(),
    1075,
    'all',
    83,
    'BRE_Rework_Prod_Copy_v5-6129',
    'all',
    'All',
    'all',
    'all'
);

INSERT INTO awsprod.business_rule (
    rule_name, rule_description, priority, workflow_id, status,
    rule_summary, created_date, created_by, FacilityID,
    version_id, rule_id_text, AWSCustomerID, part_types, idCustomertype, MaterialType
) VALUES (
    'Sanitization-Spare',
    'This part passed failure analysis testing and requires sanitization before shipping outbound to satisfy a spares demand, send to sanitization',
    36,
    '2',
    'Active',
    'input = \"Pass-Testing\" , Attribute ID Contains \"Sanitization-Spare\" , Demand Flag = \"Yes\" , Sanitization Flag = \"Yes\"  , Facility = \"All\" ',
    NOW(),
    1075,
    'all',
    83,
    'BRE_Rework_Prod_Copy_v5-6130',
    'all',
    'All',
    'all',
    'all'
);

INSERT INTO awsprod.business_rule (
    rule_name, rule_description, priority, workflow_id, status,
    rule_summary, created_date, created_by, FacilityID,
    version_id, rule_id_text, AWSCustomerID, part_types, idCustomertype, MaterialType
) VALUES (
    'Sanitization-NetworkGear',
    'Route various network gear to sanitization if they have sanitization requirement',
    37,
    '10',
    'Active',
    'input = \"Pass-Evaluation\" , Attribute ID Not Contains \"Failure_Analysis-FullTest\" , Sanitization Flag = \"Yes\" , Attribute ID Contains \"Sanitization-Recycle OR Sanitization-Destruction OR Sanitization-RMA OR Sanitization-Liquidation\"  , Facility = \"All\" ',
    NOW(),
    1075,
    'all',
    83,
    'BRE_Rework_Prod_Copy_v5-6131',
    'all',
    'AWS DWDM,AWS ROUTER,AWS SWITCH,CHASSIS,AWS NETWORK ACCESSORY',
    'all',
    'all'
);

INSERT INTO awsprod.business_rule (
    rule_name, rule_description, priority, workflow_id, status,
    rule_summary, created_date, created_by, FacilityID,
    version_id, rule_id_text, AWSCustomerID, part_types, idCustomertype, MaterialType
) VALUES (
    'Spare-3',
    'This part passed failure analysis testing and non-destructive sanitization, send to outbound shipping to satisfy a spares demand',
    38,
    '3',
    'Active',
    'input = \"Pass-Sanitization\" , Attribute ID Contains \"Sanitization-Spare\" , Demand Flag = \"Yes\"  , Facility = \"All\" ',
    NOW(),
    1075,
    'all',
    83,
    'BRE_Rework_Prod_Copy_v5-6132',
    'all',
    'All',
    'all',
    'all'
);

INSERT INTO awsprod.business_rule (
    rule_name, rule_description, priority, workflow_id, status,
    rule_summary, created_date, created_by, FacilityID,
    version_id, rule_id_text, AWSCustomerID, part_types, idCustomertype, MaterialType
) VALUES (
    'FastTrack-Spare-1',
    'This is a spare replacement part sent from the vendor, send to VMI or direct ship to DC as a spare part',
    20,
    '10',
    'Active',
    'input = \"Pass-Evaluation\" , First Arrival = \"Yes\" , Fleet Risk = \"No\" , Demand Flag = \"Yes\"  , Facility = \"All\" ',
    NOW(),
    1075,
    'all',
    83,
    'BRE_Rework_Prod_Copy_v5-6133',
    'all',
    'All',
    '3',
    'all'
);

INSERT INTO awsprod.business_rule (
    rule_name, rule_description, priority, workflow_id, status,
    rule_summary, created_date, created_by, FacilityID,
    version_id, rule_id_text, AWSCustomerID, part_types, idCustomertype, MaterialType
) VALUES (
    'RMA-Sanitized',
    'Route sanitized DWDMs to RMA-Direct if RMA eligible',
    39,
    '3',
    'Active',
    'input = \"Pass-Sanitization\" , Attribute ID Contains \"RMA-Standard\" , Attribute ID Contains \"Sanitization-RMA\"  , Facility = \"All\" ',
    NOW(),
    1075,
    'all',
    83,
    'BRE_Rework_Prod_Copy_v5-6134',
    'all',
    'All',
    'all',
    'all'
);

INSERT INTO awsprod.business_rule (
    rule_name, rule_description, priority, workflow_id, status,
    rule_summary, created_date, created_by, FacilityID,
    version_id, rule_id_text, AWSCustomerID, part_types, idCustomertype, MaterialType
) VALUES (
    'Liquidation-Sanitized',
    'Route sanitized network gear to liquidation if they are liquidation eligible',
    40,
    '3',
    'Active',
    'input = \"Pass-Sanitization\" , Attribute ID Contains \"Liquidation-Active\" , Attribute ID Contains \"Sanitization-Liquidation\"  , Facility = \"All\" ',
    NOW(),
    1075,
    'all',
    83,
    'BRE_Rework_Prod_Copy_v5-6135',
    'all',
    'All',
    'all',
    'all'
);

INSERT INTO awsprod.business_rule (
    rule_name, rule_description, priority, workflow_id, status,
    rule_summary, created_date, created_by, FacilityID,
    version_id, rule_id_text, AWSCustomerID, part_types, idCustomertype, MaterialType
) VALUES (
    'FastTrack-Repair-1',
    'This part qualifies for repair in effort to test and spare, send to repair',
    21,
    '10',
    'Active',
    'input = \"Fail-DamagedOrMissingHeatSink OR Fail-OutdatedHeatSinkOrPins\" , Attribute ID Contains \"Failure_Analysis-FullTest\" , Fleet Risk = \"No\" , Attribute ID Contains \"Repair-HeatSinkReplacement\" , Demand Flag = \"Yes\"  , Facility = \"All\" ',
    NOW(),
    1075,
    'all',
    83,
    'BRE_Rework_Prod_Copy_v5-6136',
    'all',
    'GPU,DONGLE',
    'all',
    'all'
);

INSERT INTO awsprod.business_rule (
    rule_name, rule_description, priority, workflow_id, status,
    rule_summary, created_date, created_by, FacilityID,
    version_id, rule_id_text, AWSCustomerID, part_types, idCustomertype, MaterialType
) VALUES (
    'ReturnMerchandiseAuthorization-2',
    'This will allow GPU (Nvidia) that fail testing to reroute to RMA Investigation',
    41,
    'all',
    'Active',
    'input = \"Pass-Evaluation OR Fail-Testing OR Fail-AuthenticationError OR Fail-FlapRecord OR Fail-IncompatibleForTesting OR Fail-MultipleTestErrors OR Fail-MultipleTestFail OR Fail-NoPower OR Fail-PartNotFound OR Fail-RetiredPart OR Fail-PortFailure OR Fail-FRUFailureCombination OR Fail-FRUFailureFan OR Fail-FRUFailurePSU OR Fail-FRUFailureRAM OR Fail-TestingAfterMultipleRepairs OR Fail-Overheat OR Fail-BrickUnit OR Fail-FlapReocrd OR Fail-RepeatOffender OR Fail-FA2Candidate OR Pass-Evaluation\" , Fleet Risk = \"No\" , Demand Flag = \"Yes\" , Attribute ID Contains \"RMA-Standard\" , Manufacturer = \"1024\"  , Facility = \"All\" ',
    NOW(),
    1075,
    'all',
    83,
    'BRE_Rework_Prod_Copy_v5-6137',
    'all',
    'GPU,GPU BASEBOARD,GPU BOARD ASSEMBLY',
    'all',
    'all'
);

INSERT INTO awsprod.business_rule (
    rule_name, rule_description, priority, workflow_id, status,
    rule_summary, created_date, created_by, FacilityID,
    version_id, rule_id_text, AWSCustomerID, part_types, idCustomertype, MaterialType
) VALUES (
    'FastTrack-Repair-6',
    'This part qualifies for repair in effort to test and spare, send to repair',
    22,
    '10',
    'Active',
    'input = \"Pass-Evaluation\" , First Arrival = \"Yes\" , Fleet Risk = \"No\" , Attribute ID Contains \"Failure_Analysis-FullTest\" , Attribute ID Contains \"Repair-HeatSinkReplacement\" , Demand Flag = \"Yes\"  , Facility = \"All\" ',
    NOW(),
    1075,
    'all',
    83,
    'BRE_Rework_Prod_Copy_v5-6138',
    'all',
    'GPU',
    'all',
    'all'
);

INSERT INTO awsprod.business_rule (
    rule_name, rule_description, priority, workflow_id, status,
    rule_summary, created_date, created_by, FacilityID,
    version_id, rule_id_text, AWSCustomerID, part_types, idCustomertype, MaterialType
) VALUES (
    'FailureAnalysis-DongleBypass',
    'This rule bypasses the FIRST ARRIVAL for Dongles only and allows them to be sent to FA.',
    23,
    '10',
    'Active',
    'input = \"Pass-Evaluation\" , Attribute ID Contains \"Failure_Analysis-FullTest\" , Demand Flag = \"Yes\" , Fleet Risk = \"No\"  , Facility = \"All\" \"',
    NOW(),
    1075,
    'all',
    83,
    'BRE_Rework_Prod_Copy_v5-6139',
    'all',
    'DONGLE',
    'all',
    'all'
);

INSERT INTO awsprod.business_rule (
    rule_name, rule_description, priority, workflow_id, status,
    rule_summary, created_date, created_by, FacilityID,
    version_id, rule_id_text, AWSCustomerID, part_types, idCustomertype, MaterialType
) VALUES (
    'ReturnMerchandiseAuthorization-1',
    'This part may qualify for a free replacement, send to RMA for investigation',
    42,
    'all',
    'Active',
    'input = \"Pass-Evaluation OR Fail-Testing OR Fail-AuthenticationError OR Fail-FlapRecord OR Fail-IncompatibleForTesting OR Fail-MultipleTestErrors OR Fail-MultipleTestFail OR Fail-NoPower OR Fail-PartNotFound OR Fail-RetiredPart OR Fail-PortFailure OR Fail-FRUFailureCombination OR Fail-FRUFailureFan OR Fail-FRUFailurePSU OR Fail-FRUFailureRAM OR Fail-TestingAfterMultipleRepairs OR Fail-Overheat OR Fail-BrickUnit OR Fail-FlapReocrd OR Fail-RepeatOffender OR Fail-FA2Candidate OR Pass-Evaluation\" , Demand Flag = \"Yes\" , First Arrival = \"Yes\" , Fleet Risk = \"No\" , In Warranty = \"Yes\" , Attribute ID Contains \"RMA-Standard\" , Sanitization Flag = \"No\"  , Facility = \"All\" ',
    NOW(),
    1075,
    'all',
    83,
    'BRE_Rework_Prod_Copy_v5-6140',
    'all',
    'All',
    'all',
    'all'
);

INSERT INTO awsprod.business_rule (
    rule_name, rule_description, priority, workflow_id, status,
    rule_summary, created_date, created_by, FacilityID,
    version_id, rule_id_text, AWSCustomerID, part_types, idCustomertype, MaterialType
) VALUES (
    'FailureAnalysis-1',
    'There is a need to test this part, send to failure analysis for testing',
    24,
    '10',
    'Active',
    'input = \"Pass-Evaluation\" , Attribute ID Contains \"Failure_Analysis-FullTest\" , Demand Flag = \"Yes\" , First Arrival = \"Yes\" , Fleet Risk = \"No\"  , Facility = \"All\" ',
    NOW(),
    1075,
    'all',
    83,
    'BRE_Rework_Prod_Copy_v5-6141',
    'all',
    'All',
    'all',
    'all'
);

INSERT INTO awsprod.business_rule (
    rule_name, rule_description, priority, workflow_id, status,
    rule_summary, created_date, created_by, FacilityID,
    version_id, rule_id_text, AWSCustomerID, part_types, idCustomertype, MaterialType
) VALUES (
    'FailureAnalysis-PSU_FleetRisk_Bypass',
    'Allows fleet risk PSUs through to test for RMA',
    25,
    '10',
    'Active',
    'input = \"Pass-Evaluation\" , Attribute ID Contains \"Failure_Analysis-FullTest\" , Demand Flag = \"Yes\" , First Arrival = \"Yes\"  , Facility = \"All\" \"',
    NOW(),
    1075,
    'all',
    83,
    'BRE_Rework_Prod_Copy_v5-6142',
    'all',
    'PSU',
    'all',
    'all'
);

INSERT INTO awsprod.business_rule (
    rule_name, rule_description, priority, workflow_id, status,
    rule_summary, created_date, created_by, FacilityID,
    version_id, rule_id_text, AWSCustomerID, part_types, idCustomertype, MaterialType
) VALUES (
    'ReturnToVendor-PartOfAssembly',
    'Need to route GPUs, which are part of board assemblies, to new POA disposition to enable removal for RMA and accurate counts for commercial',
    43,
    '4',
    'Active',
    'input = \"Pass-ReturnToVendorRMA-PARTOFASSEMBLY\" , , Facility = \"All\" ',
    NOW(),
    1075,
    'all',
    83,
    'BRE_Rework_Prod_Copy_v5-6143',
    'all',
    'GPU',
    'all',
    'all'
);

INSERT INTO awsprod.business_rule (
    rule_name, rule_description, priority, workflow_id, status,
    rule_summary, created_date, created_by, FacilityID,
    version_id, rule_id_text, AWSCustomerID, part_types, idCustomertype, MaterialType
) VALUES (
    'FailureAnalysis-2',
    'There is a need to test this part, send to failure analysis for testing',
    26,
    '10',
    'Active',
    'input = \"Pass-Evaluation\" , Attribute ID Contains \"Failure_Analysis-FullTest\" , First Arrival = \"Yes\" , Attribute ID Contains \"RMA-Virtual\"  , Facility = \"All\" ',
    NOW(),
    1075,
    'all',
    83,
    'BRE_Rework_Prod_Copy_v5-6144',
    'all',
    'All',
    'all',
    'all'
);

INSERT INTO awsprod.business_rule (
    rule_name, rule_description, priority, workflow_id, status,
    rule_summary, created_date, created_by, FacilityID,
    version_id, rule_id_text, AWSCustomerID, part_types, idCustomertype, MaterialType
) VALUES (
    'FailureAnalysis-3',
    'This part needs to be tested again',
    27,
    '2',
    'Active',
    'input = \"Retest-FirstTestError OR Retest-SecondTestError OR Retest-FirstTestFail OR Retest-SecondTestFail\" , , Facility = \"All\" ',
    NOW(),
    1075,
    'all',
    83,
    'BRE_Rework_Prod_Copy_v5-6145',
    'all',
    'All',
    'all',
    'all'
);

INSERT INTO awsprod.business_rule (
    rule_name, rule_description, priority, workflow_id, status,
    rule_summary, created_date, created_by, FacilityID,
    version_id, rule_id_text, AWSCustomerID, part_types, idCustomertype, MaterialType
) VALUES (
    'Spare-1',
    'This part passed failure analysis testing and satisfies a spares demand, send to VMI or direct ship to DC as a spare part',
    28,
    '2',
    'Active',
    'input = \"Pass-Testing\" , Demand Flag = \"Yes\" , Sanitization Flag = \"No\" , Fleet Risk = \"No\"  , Facility = \"All\" ',
    NOW(),
    1075,
    'all',
    83,
    'BRE_Rework_Prod_Copy_v5-6146',
    'all',
    'All',
    'all',
    'all'
);

INSERT INTO awsprod.business_rule (
    rule_name, rule_description, priority, workflow_id, status,
    rule_summary, created_date, created_by, FacilityID,
    version_id, rule_id_text, AWSCustomerID, part_types, idCustomertype, MaterialType
) VALUES (
    'RMAPartInWarranty-GPU_Sani_Bypass',
    'Allows GPUs with sanitization out to RMA',
    45,
    '4',
    'Active',
    'input = \"Pass-RMAWarrantyCheck OR Pass-ReturnToVendorRMA-PARTOFASSEMBLY\" , Sanitization Flag = \"Yes\" , Manufacturer = \"1024\"  , Facility = \"All\" ',
    NOW(),
    1075,
    'all',
    83,
    'BRE_Rework_Prod_Copy_v5-6147',
    'all',
    'GPU BASEBOARD,GPU,GPU BOARD ASSEMBLY',
    'all',
    'all'
);

INSERT INTO awsprod.business_rule (
    rule_name, rule_description, priority, workflow_id, status,
    rule_summary, created_date, created_by, FacilityID,
    version_id, rule_id_text, AWSCustomerID, part_types, idCustomertype, MaterialType
) VALUES (
    'Spare-2',
    'This part passed failure analysis testing and satisfies a spares demand, send to VMI or direct ship to DC as a spare part',
    29,
    '2',
    'Active',
    'input = \"Pass-Testing\" , Demand Flag = \"Yes\" , Sanitization Flag = \"Yes\"  , Facility = \"All\" ',
    NOW(),
    1075,
    'all',
    83,
    'BRE_Rework_Prod_Copy_v5-6148',
    'all',
    'DONGLE',
    'all',
    'all'
);

INSERT INTO awsprod.business_rule (
    rule_name, rule_description, priority, workflow_id, status,
    rule_summary, created_date, created_by, FacilityID,
    version_id, rule_id_text, AWSCustomerID, part_types, idCustomertype, MaterialType
) VALUES (
    'SpareTransfer-1',
    'This part passed failure analysis testing and satisfies a spares demand, send to DC as a spare part via Red Zone Transfer ONLY',
    30,
    '2',
    'Active',
    'input = \"Pass-Testing\" , Attribute ID Not Contains \"Sanitization-Spare\" , Demand Flag = \"Yes\" , Sanitization Flag = \"Yes\"  , Facility = \"All\" ',
    NOW(),
    1075,
    'all',
    83,
    'BRE_Rework_Prod_Copy_v5-6149',
    'all',
    'All',
    'all',
    'all'
);

INSERT INTO awsprod.business_rule (
    rule_name, rule_description, priority, workflow_id, status,
    rule_summary, created_date, created_by, FacilityID,
    version_id, rule_id_text, AWSCustomerID, part_types, idCustomertype, MaterialType
) VALUES (
    'Repair-1',
    'This part requires repairs before failure analysis testing, send to repair',
    31,
    '2',
    'Active',
    'input = \"Fail-Overheat\" , Attribute ID Contains \"Repair-HeatSinkReplacement\" , Demand Flag = \"Yes\"  , Facility = \"All\" ',
    NOW(),
    1075,
    'all',
    83,
    'BRE_Rework_Prod_Copy_v5-6150',
    'all',
    'DONGLE',
    'all',
    'all'
);

INSERT INTO awsprod.business_rule (
    rule_name, rule_description, priority, workflow_id, status,
    rule_summary, created_date, created_by, FacilityID,
    version_id, rule_id_text, AWSCustomerID, part_types, idCustomertype, MaterialType
) VALUES (
    'ReturnToVendor-ReturnMerchandiseAuthorization-1',
    'This part qualifies for free replacement, send to outbound shipping to return to the vendor',
    46,
    '4',
    'Active',
    'input = \"Pass-RMAWarrantyCheck OR Pass-ReturnToVendorRMA-PARTOFASSEMBLY\" , Sanitization Flag = \"No\"  , Facility = \"All\" \"',
    NOW(),
    1075,
    'all',
    83,
    'BRE_Rework_Prod_Copy_v5-6151',
    'all',
    'All',
    'all',
    'all'
);

INSERT INTO awsprod.business_rule (
    rule_name, rule_description, priority, workflow_id, status,
    rule_summary, created_date, created_by, FacilityID,
    version_id, rule_id_text, AWSCustomerID, part_types, idCustomertype, MaterialType
) VALUES (
    'FailureAnalysisLevel2-1',
    'This part requires further analysis, send to outbound shipping for delivery to FA2',
    47,
    '2',
    'Active',
    'input = \"Fail-FA2Candidate\" , Sanitization Flag = \"No\"  , Facility = \"All\" ',
    NOW(),
    1075,
    'all',
    83,
    'BRE_Rework_Prod_Copy_v5-6152',
    'all',
    'AWS Cable,AWS OPTICAL MODULE,AWS FIBER',
    '1',
    'all'
);

INSERT INTO awsprod.business_rule (
    rule_name, rule_description, priority, workflow_id, status,
    rule_summary, created_date, created_by, FacilityID,
    version_id, rule_id_text, AWSCustomerID, part_types, idCustomertype, MaterialType
) VALUES (
    'FailureAnalysisLevel2-2',
    'This part requires further analysis, send to outbound shipping for delivery to FA2',
    48,
    '2',
    'Active',
    'input = \"Fail-FlapRecord\" , Sanitization Flag = \"No\"  , Facility = \"All\" ',
    NOW(),
    1075,
    'all',
    83,
    'BRE_Rework_Prod_Copy_v5-6154',
    'all',
    'AWS Cable,AWS FIBER,AWS OPTICAL MODULE',
    '1',
    'all'
);

INSERT INTO awsprod.business_rule (
    rule_name, rule_description, priority, workflow_id, status,
    rule_summary, created_date, created_by, FacilityID,
    version_id, rule_id_text, AWSCustomerID, part_types, idCustomertype, MaterialType
) VALUES (
    'FailureAnalysisLevel2-3',
    'This part requires further analysis, send to outbound shipping for delivery to FA2',
    49,
    '2',
    'Active',
    'input = \"Fail-RepeatOffender\" , Sanitization Flag = \"No\"  , Facility = \"All\" ',
    NOW(),
    1075,
    'all',
    83,
    'BRE_Rework_Prod_Copy_v5-6155',
    'all',
    'AWS FIBER,AWS Cable,AWS OPTICAL MODULE',
    '1',
    'all'
);

INSERT INTO awsprod.business_rule (
    rule_name, rule_description, priority, workflow_id, status,
    rule_summary, created_date, created_by, FacilityID,
    version_id, rule_id_text, AWSCustomerID, part_types, idCustomertype, MaterialType
) VALUES (
    'Liquidation-1',
    'This part qualifies for liquidation, send to outbound for consolidation to liquidation vendor',
    50,
    'all',
    'Active',
    'input = \"Pass-Evaluation OR Pass-Testing OR Fail-Testing OR Fail-AuthenticationError OR Fail-FlapRecord OR Fail-IncompatibleForTesting OR Fail-MultipleTestErrors OR Fail-MultipleTestFail OR Fail-NoPower OR Fail-PartNotFound OR Fail-RetiredPart OR Pass-Repair OR Fail-PortFailure OR Fail-FRUFailureCombination OR Fail-FRUFailureFan OR Fail-FRUFailurePSU OR Fail-FRUFailureRAM OR Fail-TestingAfterMultipleRepairs OR Pass-FRUInstallCombination OR Pass-FRUInstallFan OR Pass-FRUInstallPSU OR Pass-FRUInstallRAM OR Pass-FRURemoveReplaceCombination OR Pass-FRURemoveReplaceFan OR Pass-FRURemoveReplacePSU OR Pass-FRURemoveReplaceRAM OR Pass-InstallHeatSink OR Pass-RemoveReplaceHeatSink OR Pass-RMAWarrantyCheck OR Fail-Overheat OR Pass-InstallThermalPastePins OR Pass-RemoveReplaceThermalPastePins OR Fail-BrickUnit OR Fail-FlapReocrd OR Fail-RepeatOffender OR Fail-FA2Candidate OR Pass-Evaluation OR Pass-ReturnToVendorRMA-PARTOFASSEMBLY\" , Sanitization Flag = \"No\" , Attribute ID Contains \"Liquidation-Active\"  , Facility = \"All\" ',
    NOW(),
    1075,
    'all',
    83,
    'BRE_Rework_Prod_Copy_v5-6156',
    'all',
    'All',
    'all',
    'all'
);

INSERT INTO awsprod.business_rule (
    rule_name, rule_description, priority, workflow_id, status,
    rule_summary, created_date, created_by, FacilityID,
    version_id, rule_id_text, AWSCustomerID, part_types, idCustomertype, MaterialType
) VALUES (
    'Recycle-4',
    'There is no higher recovery opportunity for this part, send to onsite processing for recycling',
    51,
    '5',
    'Active',
    'input = \"Pass-ChipHarvest\" , Attribute ID Contains \"Extract-HeatSinkForRepair\"  , Facility = \"All\" \"',
    NOW(),
    1075,
    'all',
    83,
    'BRE_Rework_Prod_Copy_v5-6157',
    'all',
    'DONGLE',
    'all',
    'all'
);

INSERT INTO awsprod.business_rule (
    rule_name, rule_description, priority, workflow_id, status,
    rule_summary, created_date, created_by, FacilityID,
    version_id, rule_id_text, AWSCustomerID, part_types, idCustomertype, MaterialType
) VALUES (
    'Extraction-3',
    'Send to harvest/extraction for sub component removal',
    52,
    '5',
    'Active',
    'input = \"Fail-ChipHarvest\" , Demand Flag = \"No\" , Attribute ID Contains \"Extract-HeatSinkForRepair\"  , Facility = \"All\" ',
    NOW(),
    1075,
    'all',
    83,
    'BRE_Rework_Prod_Copy_v5-6158',
    'all',
    'DONGLE',
    'all',
    'all'
);

INSERT INTO awsprod.business_rule (
    rule_name, rule_description, priority, workflow_id, status,
    rule_summary, created_date, created_by, FacilityID,
    version_id, rule_id_text, AWSCustomerID, part_types, idCustomertype, MaterialType
) VALUES (
    'Liquidation-2',
    'This part qualifies for liquidation when contractual blockers are removed, send to storage holding location for liquidation',
    53,
    'all',
    'Active',
    'input = \"Pass-Evaluation OR Pass-Testing OR Fail-Testing OR Fail-AuthenticationError OR Fail-FlapRecord OR Fail-IncompatibleForTesting OR Fail-MultipleTestErrors OR Fail-MultipleTestFail OR Fail-NoPower OR Fail-PartNotFound OR Fail-RetiredPart OR Fail-OutOfWarranty OR Fail-NotCoveredByWarranty OR Fail-MissingHostIdentification OR Fail-PortFailure OR Fail-FRUFailureCombination OR Fail-FRUFailureFan OR Fail-FRUFailurePSU OR Fail-FRUFailureRAM OR Fail-TestingAfterMultipleRepairs OR Fail-Overheat OR Fail-BrickUnit OR Fail-FlapReocrd OR Fail-RepeatOffender OR Fail-FA2Candidate OR Pass-Evaluation\" , Attribute ID Contains \"Liquidation-Delayed\" , Sanitization Flag = \"No\"  , Facility = \"All\" ',
    NOW(),
    1075,
    'all',
    83,
    'BRE_Rework_Prod_Copy_v5-6159',
    'all',
    'All',
    'all',
    'all'
);

INSERT INTO awsprod.business_rule (
    rule_name, rule_description, priority, workflow_id, status,
    rule_summary, created_date, created_by, FacilityID,
    version_id, rule_id_text, AWSCustomerID, part_types, idCustomertype, MaterialType
) VALUES (
    'Harvest-1',
    'Send to harvest for sub component removal',
    54,
    'all',
    'Active',
    'input = \"Fail-DamagedBodyChassis OR Fail-DamagedChipsPins OR Fail-DamagedCords OR Fail-DamagedFixtures OR Fail-DamagedTestAccessPoint OR Fail-ExcessiveRust OR Fail-Testing OR Fail-AuthenticationError OR Fail-FlapRecord OR Fail-MultipleTestErrors OR Fail-MultipleTestFail OR Fail-NoPower OR Fail-PartNotFound OR Fail-Repair OR Fail-DamagedOrMissingHeatSink OR Fail-PortFailure OR Fail-FRUMissingCombination OR Fail-FRUMissingFan OR Fail-FRUMissingPSU OR Fail-FRUFailureCombination OR Fail-FRUFailureFan OR Fail-FRUFailurePSU OR Fail-FRUFailureRAM OR Fail-TestingAfterMultipleRepairs OR Fail-BeyondRepair OR Fail-InsufficientConsumables OR Fail-MultipleRepairs OR Fail-Overheat OR Fail-BrickUnit OR Fail-FlapReocrd OR Fail-RepeatOffender OR Fail-FA2Candidate OR Fail-DamagedBodyChassis OR Fail-DamagedChipsPins OR Fail-DamagedCords OR Fail-DamagedFixtures OR Fail-DamagedTestAccessPoint OR Fail-ExcessiveRust OR Fail-DamagedOrMissingHeatSink OR Fail-FRUMissingCombination OR Fail-FRUMissingFan OR Fail-FRUMissingPSU OR Fail-DamagedChips OR Fail-DamagedPins\" , Attribute ID Contains \"Failure_Analysis-FullTest\" , Attribute ID Contains \"Extract-HeatSinkForRepair\" , Demand Flag = \"Yes\"  , Facility = \"All\" ',
    NOW(),
    1075,
    'all',
    83,
    'BRE_Rework_Prod_Copy_v5-6160',
    'all',
    'DONGLE,GPU',
    'all',
    'all'
);

INSERT INTO awsprod.business_rule (
    rule_name, rule_description, priority, workflow_id, status,
    rule_summary, created_date, created_by, FacilityID,
    version_id, rule_id_text, AWSCustomerID, part_types, idCustomertype, MaterialType
) VALUES (
    'HarvestDongleChipForLiquidation',
    'Send to harvest for chip sub component removal',
    55,
    '5',
    'Active',
    'input = \"Pass-ChipHarvest OR Pass-Harvest\" , MPN = \"K2T-QB\"  , Facility = \"DUB210\"',
    NOW(),
    1075,
    '11',
    83,
    'BRE_Rework_Prod_Copy_v5-6161',
    'all',
    'DONGLE',
    'all',
    'all'
);

INSERT INTO awsprod.business_rule (
    rule_name, rule_description, priority, workflow_id, status,
    rule_summary, created_date, created_by, FacilityID,
    version_id, rule_id_text, AWSCustomerID, part_types, idCustomertype, MaterialType
) VALUES (
    'HarvestDongleChipForLiquidation1',
    'Send to harvest for chip sub component removal',
    56,
    '5',
    'Active',
    'input = \"Pass-Harvest OR Pass-ChipHarvest\" , MPN = \"K2T-QB-TP1\"  , Facility = \"All\" ',
    NOW(),
    1075,
    'all',
    83,
    'BRE_Rework_Prod_Copy_v5-6162',
    'all',
    'DONGLE',
    'all',
    'all'
);

INSERT INTO awsprod.business_rule (
    rule_name, rule_description, priority, workflow_id, status,
    rule_summary, created_date, created_by, FacilityID,
    version_id, rule_id_text, AWSCustomerID, part_types, idCustomertype, MaterialType
) VALUES (
    'HarvestDongleChipForLiquidation2',
    'HarvestDongleChipForLiquidation',
    57,
    '5',
    'Active',
    'input = \"Pass-ChipHarvest OR Pass-Harvest\" , MPN = \"K2T-QB-1TPM\"  , Facility = \"All\" \"',
    NOW(),
    1075,
    'all',
    83,
    'BRE_Rework_Prod_Copy_v5-6163',
    'all',
    'DONGLE',
    'all',
    'all'
);

INSERT INTO awsprod.business_rule (
    rule_name, rule_description, priority, workflow_id, status,
    rule_summary, created_date, created_by, FacilityID,
    version_id, rule_id_text, AWSCustomerID, part_types, idCustomertype, MaterialType
) VALUES (
    'Recycle-3',
    'There is no higher recovery opportunity for this part, send to outbound shipping for recycling removal',
    58,
    '5',
    'Active',
    'input = \"Pass-Harvest OR Pass-ChipHarvest\" , , Facility = \"All\" ',
    NOW(),
    1075,
    'all',
    83,
    'BRE_Rework_Prod_Copy_v5-6164',
    'all',
    'All',
    'all',
    'all'
);

INSERT INTO awsprod.business_rule (
    rule_name, rule_description, priority, workflow_id, status,
    rule_summary, created_date, created_by, FacilityID,
    version_id, rule_id_text, AWSCustomerID, part_types, idCustomertype, MaterialType
) VALUES (
    'Recycle-NetworkGear',
    'Sanitized network gear (that require it), and non-sanitized network gear will route to HostDemanufacture for deman. This is a catchall in the event there is no other path for these parts',
    59,
    'all',
    'Active',
    'input = \"Pass-Evaluation OR Fail-DamagedBodyChassis OR Fail-DamagedChipsPins OR Fail-DamagedCords OR Fail-DamagedFixtures OR Fail-DamagedTestAccessPoint OR Fail-ExcessiveRust OR Fail-MissingCriticalPart OR Fail-AuthenticationError OR Fail-FlapRecord OR Fail-Sanitization OR Fail-OutOfWarranty OR Fail-NotCoveredByWarranty OR Fail-MissingHostIdentification OR Fail-DamagedOrMissingHeatSink OR Fail-FRUMissingCombination OR Fail-FRUMissingFan OR Fail-FRUMissingPSU OR Fail-FRUFailureCombination OR Fail-FRUFailureFan OR Fail-FRUFailurePSU OR Fail-FRUFailureRAM OR Fail-BrickUnit OR Fail-FlapReocrd OR Fail-FA2Candidate OR Fail-OutdatedHeatSinkOrPins OR Pass-Evaluation OR Fail-DamagedBodyChassis OR Fail-DamagedChipsPins OR Fail-DamagedCords OR Fail-DamagedFixtures OR Fail-DamagedTestAccessPoint OR Fail-ExcessiveRust OR Fail-MissingCriticalPart OR Fail-DamagedOrMissingHeatSink OR Fail-FRUMissingCombination OR Fail-FRUMissingFan OR Fail-FRUMissingPSU OR Fail-OutdatedHeatSinkOrPins OR Fail-ImproperPackaging OR Fail-DamagedChips OR Fail-DamagedPins OR Fail-NewMediaFail\" , , Facility = \"All\" ',
    NOW(),
    1075,
    'all',
    83,
    'BRE_Rework_Prod_Copy_v5-6165',
    'all',
    'AWS DWDM,AWS NETWORK ACCESSORY,AWS ROUTER,AWS SWITCH,CHASSIS',
    'all',
    'all'
);

INSERT INTO awsprod.business_rule (
    rule_name, rule_description, priority, workflow_id, status,
    rule_summary, created_date, created_by, FacilityID,
    version_id, rule_id_text, AWSCustomerID, part_types, idCustomertype, MaterialType
) VALUES (
    'APRecycle',
    'Annapurna Labs rule to prevent standard recycle and force recycle processed',
    60,
    'all',
    'Active',
    'input = \"Fail-DamagedBodyChassis OR Fail-DamagedChipsPins OR Fail-DamagedCords OR Fail-DamagedFixtures OR Fail-DamagedTestAccessPoint OR Fail-Testing OR Fail-AuthenticationError OR Fail-FlapRecord OR Fail-MultipleTestErrors OR Fail-MultipleTestFail OR Fail-NoPower OR Fail-PartNotFound OR Fail-Repair OR Fail-DamagedOrMissingHeatSink OR Fail-PortFailure OR Fail-NoEligibleSubComponents OR Fail-NoEligibleFRU OR Fail-Harvest OR Fail-FRUFailureCombination OR Fail-FRUFailureFan OR Fail-FRUFailurePSU OR Fail-FRUFailureRAM OR Fail-TestingAfterMultipleRepairs OR Fail-RetiredFRU OR Fail-BeyondRepair OR Fail-InsufficientConsumables OR Fail-MultipleRepairs OR Fail-Overheat OR Fail-BrickUnit OR Fail-ChipHarvest OR Fail-FlapReocrd OR Fail-RepeatOffender OR Fail-FA2Candidate OR Fail-DamagedBodyChassis OR Fail-DamagedChipsPins OR Fail-DamagedCords OR Fail-DamagedFixtures OR Fail-DamagedOrMissingHeatSink OR Fail-ChipHarvest OR Fail-Harvest OR Fail-ChipRecovery OR Fail-DamagedChips OR Fail-DamagedPins OR Fail-DamagedTestAccessPoint OR Fail-ExcessiveRust OR Fail-ExcessiveRust OR Fail-FRUMissingCombination OR Fail-FRUMissingCombination OR Fail-FRUMissingFan OR Fail-FRUMissingFan OR Fail-FRUMissingPSU OR Fail-FRUMissingPSU OR Fail-ImproperPackaging OR Fail-MissingCriticalPart OR Fail-MissingCriticalPart OR Fail-OutdatedHeatSinkOrPins OR Fail-OutdatedHeatSinkOrPins\" , Manufacturer = \"1636\"  , Facility = \"All\" ',
    NOW(),
    1075,
    'all',
    83,
    'BRE_Rework_Prod_Copy_v5-6166',
    'all',
    'GPU',
    'all',
    'all'
);

INSERT INTO awsprod.business_rule (
    rule_name, rule_description, priority, workflow_id, status,
    rule_summary, created_date, created_by, FacilityID,
    version_id, rule_id_text, AWSCustomerID, part_types, idCustomertype, MaterialType
) VALUES (
    'Recycle-1',
    'There is no higher recovery opportunity for this part, send to outbound shipping for recycling removal',
    61,
    'all',
    'Active',
    'input = \"Pass-Evaluation OR Fail-DamagedBodyChassis OR Fail-DamagedChipsPins OR Fail-DamagedCords OR Fail-DamagedFixtures OR Fail-DamagedTestAccessPoint OR Fail-ExcessiveRust OR Fail-MissingCriticalPart OR Sideline-ProblemSolve OR Pass-Testing OR Fail-Testing OR Fail-AuthenticationError OR Fail-FlapRecord OR Fail-IncompatibleForTesting OR Fail-MultipleTestErrors OR Fail-MultipleTestFail OR Fail-NoPower OR Fail-PartNotFound OR Fail-RetiredPart OR Retest-FirstTestError OR Retest-SecondTestError OR Retest-FirstTestFail OR Retest-SecondTestFail OR Pass-Sanitization OR Fail-Sanitization OR Fail-OutOfWarranty OR Fail-NotCoveredByWarranty OR Fail-MissingHostIdentification OR Pass-Harvest OR Pass Removal OR testwfI OR ds OR Fail-FireFightingOctopus OR Pass-Repair OR Fail-Repair OR Fail-DamagedOrMissingHeatSink OR Fail-MissingPSUFRU OR Fail-MissingFanFRU OR Fail-MissingCombinationFRU OR Fail-PSUFRUFailure OR Fail-FanFRUFailure OR Fail-RAMFRUFailure OR Fail-CombinationFRUFailure OR Fail-BrickedUnit OR Fail-PortFailure OR Fail-NoEligibleSubComponents OR Fail-NoEligibleFRU OR Fail-Harvest OR Fail-FRUMissingCombination OR Fail-FRUMissingFan OR Fail-FRUMissingPSU OR Fail-FRUFailureCombination OR Fail-FRUFailureFan OR Fail-FRUFailurePSU OR Fail-FRUFailureRAM OR Fail-TestingAfterMultipleRepairs OR Fail-RetiredFRU OR Pass-FRUInstallCombination OR Pass-FRUInstallFan OR Pass-FRUInstallPSU OR Pass-FRUInstallRAM OR Pass-FRURemoveReplaceCombination OR Pass-FRURemoveReplaceFan OR Pass-FRURemoveReplacePSU OR Pass-FRURemoveReplaceRAM OR Pass-InstallHeatSink OR Pass-RemoveReplaceHeatSink OR Fail-BeyondRepair OR Fail-InsufficientConsumables OR Fail-MultipleRepairs OR Pass-RMAWarrantyCheck OR Fail-Overheat OR Pass-InstallThermalPastePins OR Pass-RemoveReplaceThermalPastePins OR Fail-BrickUnit OR Pass-ChipHarvest OR Fail-ChipHarvest OR Fail-FlapReocrd OR Fail-RepeatOffender OR Fail-FA2Candidate OR Fail-OutdatedHeatSinkOrPins OR Pass-Evaluation OR Fail-DamagedBodyChassis OR Fail-DamagedChipsPins OR Fail-DamagedCords OR Fail-DamagedFixtures OR Fail-DamagedTestAccessPoint OR Fail-ExcessiveRust OR Fail-MissingCriticalPart OR Fail-DamagedOrMissingHeatSink OR Fail-FRUMissingCombination OR Fail-FRUMissingFan OR Fail-FRUMissingPSU OR Fail-OutdatedHeatSinkOrPins OR Pass-ChipHarvest OR Fail-ChipHarvest OR Pass-Harvest OR Fail-Harvest OR Pass-ChipRecovery OR Fail-ChipRecovery OR Fail-ImproperPackaging OR Fail-DamagedChassisOrEnclosure OR Fail-DamagedChips OR Fail-DamagedPins OR Fail-DamagedOrMissingHeatSink OR Fail-NewMediaFail OR Pass-ReturnToVendorRMA-PARTOFASSEMBLY\" , Sanitization Flag = \"No\"  , Facility = \"All\" ',
    NOW(),
    1075,
    'all',
    83,
    'BRE_Rework_Prod_Copy_v5-6167',
    'all',
    'All',
    'all',
    'all'
);

INSERT INTO awsprod.business_rule (
    rule_name, rule_description, priority, workflow_id, status,
    rule_summary, created_date, created_by, FacilityID,
    version_id, rule_id_text, AWSCustomerID, part_types, idCustomertype, MaterialType
) VALUES (
    'Recycle-2',
    'There is no higher recovery opportunity for this part, send to outbound shipping for recycling removal',
    62,
    'all',
    'Active',
    'input = \"Pass-Evaluation OR Fail-DamagedBodyChassis OR Fail-DamagedChipsPins OR Fail-DamagedCords OR Fail-DamagedFixtures OR Fail-DamagedTestAccessPoint OR Fail-ExcessiveRust OR Fail-MissingCriticalPart OR Sideline-ProblemSolve OR Pass-Testing OR Fail-Testing OR Fail-AuthenticationError OR Fail-FlapRecord OR Fail-IncompatibleForTesting OR Fail-MultipleTestErrors OR Fail-MultipleTestFail OR Fail-NoPower OR Fail-PartNotFound OR Fail-RetiredPart OR Retest-FirstTestError OR Retest-SecondTestError OR Retest-FirstTestFail OR Retest-SecondTestFail OR Pass-Sanitization OR Fail-Sanitization OR Fail-OutOfWarranty OR Fail-NotCoveredByWarranty OR Fail-MissingHostIdentification OR Pass-Harvest OR Pass Removal OR testwfI OR ds OR Fail-FireFightingOctopus OR Pass-Repair OR Fail-Repair OR Fail-DamagedOrMissingHeatSink OR Fail-MissingPSUFRU OR Fail-MissingFanFRU OR Fail-MissingCombinationFRU OR Fail-PSUFRUFailure OR Fail-FanFRUFailure OR Fail-RAMFRUFailure OR Fail-CombinationFRUFailure OR Fail-BrickedUnit OR Fail-PortFailure OR Fail-NoEligibleSubComponents OR Fail-NoEligibleFRU OR Fail-Harvest OR Fail-FRUMissingCombination OR Fail-FRUMissingFan OR Fail-FRUMissingPSU OR Fail-FRUFailureCombination OR Fail-FRUFailureFan OR Fail-FRUFailurePSU OR Fail-FRUFailureRAM OR Fail-TestingAfterMultipleRepairs OR Fail-RetiredFRU OR Pass-FRUInstallCombination OR Pass-FRUInstallFan OR Pass-FRUInstallPSU OR Pass-FRUInstallRAM OR Pass-FRURemoveReplaceCombination OR Pass-FRURemoveReplaceFan OR Pass-FRURemoveReplacePSU OR Pass-FRURemoveReplaceRAM OR Pass-InstallHeatSink OR Pass-RemoveReplaceHeatSink OR Fail-BeyondRepair OR Fail-InsufficientConsumables OR Fail-MultipleRepairs OR Pass-RMAWarrantyCheck OR Fail-Overheat OR Pass-InstallThermalPastePins OR Pass-RemoveReplaceThermalPastePins OR Fail-BrickUnit OR Pass-ChipHarvest OR Fail-ChipHarvest OR Fail-FlapReocrd OR Fail-RepeatOffender OR Fail-FA2Candidate OR Fail-OutdatedHeatSinkOrPins OR Pass-Evaluation OR Fail-DamagedBodyChassis OR Fail-DamagedChipsPins OR Fail-DamagedCords OR Fail-DamagedFixtures OR Fail-DamagedTestAccessPoint OR Fail-ExcessiveRust OR Fail-MissingCriticalPart OR Fail-DamagedOrMissingHeatSink OR Fail-FRUMissingCombination OR Fail-FRUMissingFan OR Fail-FRUMissingPSU OR Fail-OutdatedHeatSinkOrPins OR Pass-ChipHarvest OR Fail-ChipHarvest OR Pass-Harvest OR Fail-Harvest OR Pass-ChipRecovery OR Fail-ChipRecovery OR Fail-ImproperPackaging OR Fail-DamagedChassisOrEnclosure OR Fail-DamagedChips OR Fail-DamagedPins OR Fail-DamagedOrMissingHeatSink OR Fail-NewMediaFail OR Pass-ReturnToVendorRMA-PARTOFASSEMBLY\" , , Facility = \"All\" ',
    NOW(),
    1075,
    'all',
    83,
    'BRE_Rework_Prod_Copy_v5-6168',
    'all',
    'All',
    'all',
    'all'
);

INSERT INTO awsprod.business_rule (
    rule_name, rule_description, priority, workflow_id, status,
    rule_summary, created_date, created_by, FacilityID,
    version_id, rule_id_text, AWSCustomerID, part_types, idCustomertype, MaterialType
) VALUES (
    'PreRepairToRepair',
    'This part should be repaired before it is tested to determine suitability in the fleet, send to failure analysis testing',
    32,
    '7',
    'Active',
    'input = \"Pass-RemoveReplaceHeatSink\" , Demand Flag = \"Yes\"  , Facility = \"All\" ',
    NOW(),
    1075,
    'all',
    83,
    'BRE_Rework_Prod_Copy_v5-6169',
    'all',
    'All',
    'all',
    'all'
);

INSERT INTO awsprod.business_rule (
    rule_name, rule_description, priority, workflow_id, status,
    rule_summary, created_date, created_by, FacilityID,
    version_id, rule_id_text, AWSCustomerID, part_types, idCustomertype, MaterialType
) VALUES (
    'FailureAnalysis-4',
    'This repaired part should be tested to determine suitability in the fleet, send to failure analysis testing',
    33,
    '7',
    'Active',
    'input = \"Pass-InstallHeatSink OR Pass-RemoveReplaceHeatSink\" , Attribute ID Contains \"Repair-HeatSinkReplacement\" , Demand Flag = \"Yes\"  , Facility = \"All\" ',
    NOW(),
    1075,
    'all',
    83,
    'BRE_Rework_Prod_Copy_v5-6170',
    'all',
    'DONGLE',
    'all',
    'all'
);

INSERT INTO awsprod.business_rule (
    rule_name, rule_description, priority, workflow_id, status,
    rule_summary, created_date, created_by, FacilityID,
    version_id, rule_id_text, AWSCustomerID, part_types, idCustomertype, MaterialType
) VALUES (
    'ReturntoVendor-RMADirect',
    'This path will direct part to OEM vendor',
    44,
    '4',
    'Active',
    'input = \"Pass-RMAWarrantyCheck OR Pass-ReturnToVendorRMA-PARTOFASSEMBLY\" , RMA Direct = \"Yes\"  , Facility = \"All\" ',
    NOW(),
    1075,
    'all',
    83,
    'BRE_Rework_Prod_Copy_v5-6171',
    'all',
    'All',
    'all',
    'all'
);

