<div class="page" data-ng-controller="disposition_override_assets">
    <div class="row ui-section">            
        <div class="col-md-12">
            <article class="article">

                <script type="text/ng-template" id="password.html">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            TPVR for Disposition Override for Assets
                        </div>
                        <div class="panel-body">
                            <div class="form-horizontal verification-form">                                
                                <form name="tpvForm">
                                    <md-input-container class="md-block">
                                        <label>Audit Controller</label>
                                        <input required name="AuditController" id="AuditController" ng-model="confirmDetails.AuditController" ng-maxlength="50" type="text" autocomplete="off">
                                        <div ng-messages="tpvForm.AuditController.$error" multiple ng-if='tpvForm.AuditController.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                            <div ng-message="maxlength">Max length 50.</div>
                                        </div>
                                    </md-input-container>

                                    <md-input-container class="md-block">
                                        <label>Password</label>
                                        <input required name="Password" id="Password" ng-model="confirmDetails.Password" ng-maxlength="50" type="password" ng-enter="hide()" autocomplete="off">
                                        <div ng-messages="tpvForm.Password.$error" multiple ng-if='tpvForm.Password.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                            <div ng-message="maxlength">Max length 50.</div>
                                        </div>
                                    </md-input-container>
                                </form>
                            </div>

                        </div>

                        <div class="panel-footer text-center">
                            <span class="help-block color-danger">* Controller should be different from Login user</span>
                            <button type="button" style="margin-right:8px;" class="md-button md-raised btn-w-md  md-default" ng-click="cancel()">Close</button>
                            <button type="button" class="md-button md-raised btn-w-md  md-primary" ng-click="hide()" ng-disabled="!confirmDetails.Password">Continue</button>
                        </div>
                    </div>
                </script>

                <md-card class="no-margin-h">
                    <md-toolbar class="md-table-toolbar md-default">
                        <div class="md-toolbar-tools">
                            <span>Disposition Override Assets</span>     
                            <span class="color-danger" style="margin-left: 5px;font-size: 75%;">This page is for single or multiple SN(s) Override</span>
                        </div>
                    </md-toolbar>

                    <div class="row">
                        <div class="col-md-12">
                            <form name="material_signup_form" class="form-validation" >                                
                                <div class="col-md-3">
                                    <md-input-container class="md-block">
                                        <label>Disposition From</label>   
                                        <md-select name="FromDispositionID" ng-model="record.FromDispositionID" required aria-label="select" ng-change="CheckDispositionOverrideEligibility()" ng-disabled="record.OverrideID">
                                            <md-option ng-repeat="dis_position in AllDispositions" value="{{dis_position.disposition_id}}"> {{dis_position.disposition}} <span style="color:red;" ng-show="dis_position.sub_disposition > 0">(Sub Disposition)</span> </md-option>
                                        </md-select>
                                        <div class="error-space">
                                            <div ng-messages="material_signup_form.FromDispositionID.$error" multiple ng-if='material_signup_form.FromDispositionID.$dirty'>
                                                <div ng-message="required">This is required.</div>
                                            </div>    
                                        </div> 
                                    </md-input-container>
                                </div>

                                <div class="col-md-3">
                                    <md-input-container class="md-block">
                                        <label>Disposition To</label>   
                                        <md-select name="ToDispositionID" ng-model="record.ToDispositionID" required aria-label="select" ng-change="CheckDispositionOverrideEligibility()" ng-disabled="record.OverrideID">
                                            <md-option ng-repeat="dis_position in AllDispositions" value="{{dis_position.disposition_id}}"> {{dis_position.disposition}} <span style="color:red;" ng-show="dis_position.sub_disposition > 0">(Sub Disposition)</span> </md-option>
                                        </md-select>
                                        <div class="error-space">
                                            <div ng-messages="material_signup_form.ToDispositionID.$error" multiple ng-if='material_signup_form.ToDispositionID.$dirty'>
                                                <div ng-message="required">This is required.</div>
                                            </div>    
                                        </div> 
                                    </md-input-container>
                                </div>

                                <div class="col-md-3">
                                    <md-switch class="mt-10" ng-model="record.Eligible" aria-label="Notes Required" ng-disabled="1"> Eligible for Disposition Override</md-switch>
                                </div>

                                <div class="col-md-3" ng-if="record.ToDispositionID == '4'">
                                    <md-input-container class="md-block">
                                        <label>Sanitization Procedure</label>   
                                        <md-select name="rule_id" ng-model="record.rule_id" required aria-label="select" ng-disabled="record.OverrideID">
                                            <md-option ng-repeat="rule in BusinessRules" value="{{rule.rule_id}}"> {{rule.rule_name}} </md-option>
                                        </md-select>
                                        <div class="error-space">
                                            <div ng-messages="material_signup_form.rule_id.$error" multiple ng-if='material_signup_form.rule_id.$dirty'>
                                                <div ng-message="required">This is required.</div>
                                            </div>    
                                        </div> 
                                    </md-input-container>
                                </div>

                                <div class="col-md-3">
                                    <md-input-container class="md-block">
                                        <label>Requester Login</label>
                                        <input name="RequesterLogin" ng-model="record.RequesterLogin" required ng-disabled="1" />
                                        <div class="error-sapce">
                                            <div ng-messages="material_signup_form.RequesterLogin.$error" multiple ng-if='material_signup_form.RequesterLogin.$dirty'>
                                                <div ng-message="required">This is required.</div>
                                            </div>
                                        </div>
                                    </md-input-container>
                                </div>

                                <div class="col-md-4">
                                    <md-input-container class="md-block">
                                        <label>To Bin Name</label>
                                        <input name="BinName" ng-model="record.BinName" required ng-disabled="!record.ToDispositionID || record.OverrideID" ng-enter="ValidateBinDisposition()" />
                                        <md-button class="md-fab md-raised md-mini md-accent md-fab-bottom-right" ng-disabled="!record.BinName" ng-click="ValidateBinDisposition()">
                                            <md-icon md-svg-src="../assets/images/search.svg"></md-icon>
                                        </md-button>
                                        <div class="error-sapce">
                                            <div ng-messages="material_signup_form.BinName.$error" multiple ng-if='material_signup_form.BinName.$dirty'>
                                                <div ng-message="required">This is required.</div>
                                            </div>
                                        </div>
                                    </md-input-container>
                                </div>

                                <div class="col-md-2">
                                    <md-input-container class="md-block">
                                        <label>Asset Quantity</label>
                                        <input name="AssetQuantity" ng-model="record.AssetQuantity" required ng-disabled="1" />                                        
                                        <div class="error-sapce">
                                            <div ng-messages="material_signup_form.AssetQuantity.$error" multiple ng-if='material_signup_form.AssetQuantity.$dirty'>
                                                <div ng-message="required">This is required.</div>
                                            </div>
                                        </div>
                                    </md-input-container>
                                </div>

                                <div class="col-md-3">
                                    <md-input-container class="md-block">
                                        <label>Override Reason</label>
                                        <!-- <input name="OverrideReason" ng-model="record.OverrideReason" required /> -->
                                        <md-select name="OverrideReason" ng-model="record.OverrideReason" required aria-label="select" ng-change="ValidateNotesRequired();GetCurrentTime(record,'notes_scan_time')" ng-disabled="record.OverrideID">
                                            <md-option value="{{rea.ReasonID}}" ng-repeat="rea in OverrideReasons">{{rea.OverrideReason}}</md-option>                                            
                                        </md-select>

                                        <div class="error-sapce">
                                            <div ng-messages="material_signup_form.OverrideReason.$error" multiple ng-if='material_signup_form.OverrideReason.$dirty'>
                                                <div ng-message="required">This is required.</div>
                                            </div>
                                        </div>
                                    </md-input-container>
                                </div>  
                                
                                
                                <div class="col-md-3" ng-if="! record.NotesRequired">
                                    <md-input-container class="md-block">
                                        <label>Notes</label>
                                        <input name="Notes" ng-model="record.Notes"  ng-maxlength="100" required ng-disabled="true" />                                        
                                        <div class="error-sapce">
                                            <div ng-messages="material_signup_form.Notes.$error" multiple ng-if='material_signup_form.Notes.$dirty'>
                                                <div ng-message="required">This is required.</div>                                                
                                                <div ng-message="maxlength">Max length 100.</div>                           
                                            </div>
                                        </div>
                                    </md-input-container>
                                </div>

                                <div class="col-md-3" ng-if="record.NotesRequired">
                                    <md-input-container class="md-block">
                                        <label>Notes</label>                                
                                        <input name="Notes" ng-model="record.Notes" ng-minlength="10" ng-maxlength="100" id="Notes" list="UserInputList" required ng-disabled="record.OverrideID" ng-enter="CreateDispositionOverride()" />
                                        <datalist id="UserInputList">
                                            <option value="{{input.UserInput}}" ng-repeat="input in UserInputs">                                              
                                        </datalist>

                                        <div class="error-sapce">
                                            <div ng-messages="material_signup_form.Notes.$error" multiple ng-if='material_signup_form.Notes.$dirty'>
                                                <div ng-message="required">This is required.</div>
                                                <div ng-message="minlength">Min length 10.</div>
                                                <div ng-message="maxlength">Max length 100.</div>                           
                                            </div>
                                        </div>
                                    </md-input-container>
                                </div>

                                <!-- <div class="col-md-3">
                                    <md-input-container class="md-block includedsearch">
                                        <label>SN</label>
                                        <input required name="SerialNumber" ng-model="record.SerialNumber" required ng-maxlength="100" ng-minlength="3" ng-enter="UpdateSerialDisposition()">
                                        <md-button class="md-fab md-raised md-mini md-accent md-fab-bottom-right" ng-disabled="!record.SerialNumber" ng-click="UpdateSerialDisposition()">
                                            <md-icon md-svg-src="../assets/images/search.svg"></md-icon>
                                        </md-button>
                                        <div class="error-sapce">
                                            <div ng-messages="material_signup_form.SerialNumber.$error" multiple ng-if='material_signup_form.SerialNumber.$dirty'>                            
                                                <div ng-message="required">This is required.</div> 
                                                <div ng-message="minlength">Min length 3.</div>
                                                <div ng-message="maxlength">Max length 100.</div>                           
                                            </div>
                                        </div>
                                    </md-input-container>
                                </div> -->


                                <div class="col-md-12 btns-row">
                                    <a ng-click="ReloadPage()" style="text-decoration: none;">
                                        <md-button class="md-button md-raised btn-w-md  md-default">
                                            Cancel
                                        </md-button>
                                    </a> 
                                    <md-button class="md-raised btn-w-md md-primary btn-w-md"
                                        data-ng-disabled="material_signup_form.$invalid || record.busy || record.OverrideID" ng-click="CreateDispositionOverride()">
                                        <span ng-show="! record.busy">Save</span>
                                        <span ng-show="record.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span>
                                    </md-button>
                                </div>

                            </form>
                        </div>
                    </div>
                </md-card>


                <md-card class="no-margin-h" ng-if="record.OverrideID">
                    <md-toolbar class="md-table-toolbar md-default">
                        <div class="md-toolbar-tools">
                            <span>Scan Assets</span>     
                            <div flex ></div>
                            <span >Total Count: <strong class="text-success">{{ScannedSerials.length}}</strong></span>                       
                        </div>
                    </md-toolbar>

                    <div class="row">
                        <div class="col-md-12">

                            <div class="col-md-3 col-md-offset-3">
                                <md-input-container class="md-block includedsearch">
                                    <label>SN</label>
                                    <input required name="SerialNumber" ng-model="record.SerialNumber" required ng-maxlength="100" ng-minlength="3" ng-enter="ValidateSerial()" id="SerialNumber" ng-disabled="record.validatingSerial">
                                    <md-button class="md-fab md-raised md-mini md-accent md-fab-bottom-right" ng-disabled="!record.SerialNumber || record.validatingSerial" ng-click="ValidateSerial()">
                                        <md-icon md-svg-src="../assets/images/search.svg" ng-show="!record.validatingSerial"></md-icon>
                                        <md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" ng-show="record.validatingSerial"></md-progress-circular>
                                    </md-button>
                                    <div class="error-sapce">
                                        <div ng-messages="material_signup_form.SerialNumber.$error" multiple ng-if='material_signup_form.SerialNumber.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                            <div ng-message="minlength">Min length 3.</div>
                                            <div ng-message="maxlength">Max length 100.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>


                            <div class="col-md-12" ng-show="ScannedSerials.length > 0">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>Delete</th>
                                            <th>Serial Number</th>
                                            <th>MPN</th>
                                            <th>Part Type</th>
                                            <th>From Disposition</th>
                                            <th>To Disposition</th>
                                            <th>To Bin</th>
                                            <th>Reason</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr ng-repeat="rec in ScannedSerials">
                                            <td><span style="color: red;cursor: pointer;" ng-click="DeleteSerial(rec,$index,$event)">Delete</span></td>
                                            <td>{{rec.SerialNumber}}</td>
                                            <td>{{rec.MPN}}</td>
                                            <td>{{rec.part_type}}</td>
                                            <td>{{rec.FromDisposition}}</td>
                                            <td>{{rec.ToDisposition}}</td>
                                            <td>{{rec.ToBinName}}</td>
                                            <td>{{rec.OverrideReason}}</td>
                                        </tr>
                                    </tbody>
                                </table>

                                <div class="col-md-12 btns-row" ng-show="ScannedSerials.length > 0">                                    
                                    <md-button class="md-raised btn-w-md md-primary btn-w-md"
                                        data-ng-disabled="record.busy" ng-click="DispositionOverrideTPVR($event)">
                                        <span ng-show="! record.busy">Confirm</span>
                                        <span ng-show="record.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span>
                                    </md-button>
                                    <span class="help-block color-danger">Unable to undo Override once confirmed</span>
                                </div>

                            </div>

                        </div>
                    </div>
                </md-card>

            </article>
        </div>
    </div>
</div>