<div class="page" data-ng-controller="BusinessRule">

    <div class="row ui-section">
        <div class="col-md-12">
            <article class="article">
                <form name="BRForm">
                    <!--Create Rule Start-->
                    <md-card class="no-margin-h mt-0">

                        <md-toolbar class="md-table-toolbar md-default">
                            <div class="md-toolbar-tools">
                                <span>Rule Details</span>
                                <div flex></div>
                            <a href="#!/BusinessRuleList" class="md-button md-raised btn-w-md" style="display: flex;">
                                <i class="material-icons">chevron_left</i> Back to List
                            </a>
                            </div>
                        </md-toolbar>

                        <div class="row">
                            <div class="col-md-12">
                                <!-- {{business_rule}} -->
                                <!-- 1. Rule Name -->
                                <div class="col-md-3">
                                    <md-input-container class="md-block">
                                        <label>Rule Name</label>
                                        <input type="text" required name="rule_name" placeholder="Enter Rule Name" ng-model="business_rule.rule_name" ng-maxlength="200" ng-disabled="business_rule.rule_id">
                                        <div ng-messages="BRForm.rule_name.$error" multiple ng-if='BRForm.rule_name.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                            <div ng-message="maxlength">Max length 200.</div>
                                        </div>
                                    </md-input-container>
                                </div>

                                <!-- 2. Rule Description -->
                                <div class="col-md-3">
                                    <md-input-container class="md-block">
                                        <label>Rule Description</label>
                                        <input type="text" required name="rule_description" placeholder="Enter Description Here" ng-model="business_rule.rule_description"  ng-maxlength="500">
                                        <div class="error-sapce">
                                            <div ng-messages="BRForm.rule_description.$error" multiple ng-if='BRForm.rule_description.$dirty'>
                                                <div ng-message="required">This is required.</div>
                                                <div ng-message="maxlength">Max length 500.</div>
                                            </div>
                                        </div>
                                    </md-input-container>
                                </div>

                                <!-- 3. Priority -->
                                <div class="col-md-3">
                                    <md-input-container class="md-block">
                                        <label>Priority</label>
                                        <md-select name="priority" ng-model="business_rule.priority" required>
                                            <md-option value="1">Priority 1</md-option>
                                            <md-option value="2">Priority 2</md-option>
                                            <md-option value="3">Priority 3</md-option>
                                            <md-option value="4">Priority 4</md-option>
                                            <md-option value="5">Priority 5</md-option>
                                            <md-option value="6">Priority 6</md-option>
                                            <md-option value="7">Priority 7</md-option>
                                            <md-option value="8">Priority 8</md-option>
                                            <md-option value="9">Priority 9</md-option>
                                            <md-option value="10">Priority 10</md-option>
                                            <md-option value="11">Priority 11</md-option>
                                            <md-option value="12">Priority 12</md-option>
                                            <md-option value="13">Priority 13</md-option>
                                            <md-option value="14">Priority 14</md-option>
                                            <md-option value="15">Priority 15</md-option>
                                            <md-option value="16">Priority 16</md-option>
                                            <md-option value="17">Priority 17</md-option>
                                            <md-option value="18">Priority 18</md-option>
                                            <md-option value="19">Priority 19</md-option>
                                            <md-option value="20">Priority 20</md-option>
                                            <md-option value="21">Priority 21</md-option>
                                            <md-option value="22">Priority 22</md-option>
                                            <md-option value="23">Priority 23</md-option>
                                            <md-option value="24">Priority 24</md-option>
                                            <md-option value="25">Priority 25</md-option>
                                            <md-option value="26">Priority 26</md-option>
                                            <md-option value="27">Priority 27</md-option>
                                            <md-option value="28">Priority 28</md-option>
                                            <md-option value="29">Priority 29</md-option>
                                            <md-option value="30">Priority 30</md-option>

                                            <md-option value="31">Priority 31</md-option>
                                            <md-option value="32">Priority 32</md-option>
                                            <md-option value="33">Priority 33</md-option>
                                            <md-option value="34">Priority 34</md-option>
                                            <md-option value="35">Priority 35</md-option>
                                            <md-option value="36">Priority 36</md-option>
                                            <md-option value="37">Priority 37</md-option>
                                            <md-option value="38">Priority 38</md-option>
                                            <md-option value="39">Priority 39</md-option>
                                            <md-option value="40">Priority 40</md-option>

                                            <md-option value="41">Priority 41</md-option>
                                            <md-option value="42">Priority 42</md-option>
                                            <md-option value="43">Priority 43</md-option>
                                            <md-option value="44">Priority 44</md-option>
                                            <md-option value="45">Priority 45</md-option>
                                            <md-option value="46">Priority 46</md-option>
                                            <md-option value="47">Priority 47</md-option>
                                            <md-option value="48">Priority 48</md-option>
                                            <md-option value="49">Priority 49</md-option>
                                            <md-option value="50">Priority 50</md-option>

                                            <md-option value="51">Priority 51</md-option>
                                            <md-option value="52">Priority 52</md-option>
                                            <md-option value="53">Priority 53</md-option>
                                            <md-option value="54">Priority 54</md-option>
                                            <md-option value="55">Priority 55</md-option>
                                            <md-option value="56">Priority 56</md-option>
                                            <md-option value="57">Priority 57</md-option>
                                            <md-option value="58">Priority 58</md-option>
                                            <md-option value="59">Priority 59</md-option>
                                            <md-option value="60">Priority 60</md-option>

                                            <md-option value="61">Priority 61</md-option>
                                            <md-option value="62">Priority 62</md-option>
                                            <md-option value="63">Priority 63</md-option>
                                            <md-option value="64">Priority 64</md-option>
                                            <md-option value="65">Priority 65</md-option>
                                            <md-option value="66">Priority 66</md-option>
                                            <md-option value="67">Priority 67</md-option>
                                            <md-option value="68">Priority 68</md-option>
                                            <md-option value="69">Priority 69</md-option>
                                            <md-option value="70">Priority 70</md-option>

                                            <md-option value="71">Priority 71</md-option>
                                            <md-option value="72">Priority 72</md-option>
                                            <md-option value="73">Priority 73</md-option>
                                            <md-option value="74">Priority 74</md-option>
                                            <md-option value="75">Priority 75</md-option>
                                            <md-option value="76">Priority 76</md-option>
                                            <md-option value="77">Priority 77</md-option>
                                            <md-option value="78">Priority 78</md-option>
                                            <md-option value="79">Priority 79</md-option>
                                            <md-option value="80">Priority 80</md-option>

                                            <md-option value="81">Priority 81</md-option>
                                            <md-option value="82">Priority 82</md-option>
                                            <md-option value="83">Priority 83</md-option>
                                            <md-option value="84">Priority 84</md-option>
                                            <md-option value="85">Priority 85</md-option>
                                            <md-option value="86">Priority 86</md-option>
                                            <md-option value="87">Priority 87</md-option>
                                            <md-option value="88">Priority 88</md-option>
                                            <md-option value="89">Priority 89</md-option>
                                            <md-option value="90">Priority 90</md-option>

                                            <md-option value="91">Priority 91</md-option>
                                            <md-option value="92">Priority 92</md-option>
                                            <md-option value="93">Priority 93</md-option>
                                            <md-option value="94">Priority 94</md-option>
                                            <md-option value="95">Priority 95</md-option>
                                            <md-option value="96">Priority 96</md-option>
                                            <md-option value="97">Priority 97</md-option>
                                            <md-option value="98">Priority 98</md-option>
                                            <md-option value="99">Priority 99</md-option>
                                            <md-option value="100">Priority 100</md-option>

                                        </md-select>
                                        <div class="error-sapce">
                                            <div ng-messages="BRForm.priority.$error" multiple ng-if='BRForm.priority.$dirty'>
                                                <div ng-message="required">This is required.</div>
                                            </div>
                                        </div>
                                    </md-input-container>
                                </div>

                                <!-- 4. Version -->
                                <div class="col-md-3">
                                    <md-input-container class="md-block">
                                        <label>Version</label>
                                        <md-select name="version_id" ng-model="business_rule.version_id" required>
                                            <md-option value="{{rv.version_id}}" ng-repeat="rv in RuleVersions">{{rv.version_name}}<span style="color:red;" ng-show="rv.current_version == '1'"> (Current Version)</span><span style="color:orange;" ng-show="rv.previous_version == '1'"> (Previous Version)</span></md-option>
                                        </md-select>
                                        <div class="error-sapce">
                                            <div ng-messages="BRForm.version_id.$error" multiple ng-if='BRForm.version_id.$dirty'>
                                                <div ng-message="required">This is required.</div>
                                            </div>
                                        </div>
                                    </md-input-container>
                                </div>

                                <!-- 5. Customer ID -->
                                <div class="col-md-3">
                                    <md-input-container class="md-block">
                                        <label>Customer ID</label>
                                        <md-select name="customer_id" ng-model="business_rule.AWSCustomerID" required ng-disabled="business_rule.isGlobalRule">
                                            <md-option value="all">All</md-option>
                                            <md-option value="{{customer.AWSCustomerID}}" ng-repeat="customer in AWSCustomers">{{customer.Customer}}</md-option>
                                        </md-select>
                                        <div class="error-sapce">
                                            <div ng-messages="BRForm.customer_id.$error" multiple ng-if='BRForm.customer_id.$dirty'>
                                                <div ng-message="required">This is required.</div>
                                            </div>
                                        </div>
                                    </md-input-container>
                                </div>

                                <!-- 6. Facility -->
                                <div class="col-md-3">
                                    <md-input-container class="md-block">
                                        <label>Facility</label>
                                        <md-select name="FacilityID" ng-model="business_rule.FacilityID" required ng-disabled="business_rule.isGlobalRule">
                                            <md-option value="all">All</md-option>
                                            <md-option ng-repeat="facility in Facility | filter:{ FacilityStatus : '!InActive'}" value="{{facility.FacilityID}}"> {{facility.FacilityName}} </md-option>
                                        </md-select>
                                        <div class="error-sapce">
                                            <div ng-messages="BRForm.FacilityID.$error" multiple ng-if='BRForm.FacilityID.$dirty'>
                                                <div ng-message="required">This is required.</div>
                                            </div>
                                        </div>
                                    </md-input-container>
                                </div>

                                <!-- 7. Workflow -->
                                <div class="col-md-3">
                                    <md-input-container class="md-block">
                                        <label>Workflow</label>
                                        <md-select name="workflow_id" ng-model="business_rule.workflow_id" required ng-change="GetInputResults()" ng-disabled="business_rule.isGlobalRule">
                                            <md-option value="all">All</md-option>
                                            <md-option value="{{wf.workflow_id}}" ng-repeat="wf in WorkFlows">{{wf.workflow}}</md-option>
                                        </md-select>
                                        <div class="error-sapce">
                                            <div ng-messages="BRForm.workflow_id.$error" multiple ng-if='BRForm.workflow_id.$dirty'>
                                                <div ng-message="required">This is required.</div>
                                            </div>
                                        </div>
                                    </md-input-container>
                                </div>

                                <!-- 8. Part Type -->
                                <div class="col-md-3">
                                    <md-input-container class="md-block">
                                        <label>Part Type</label>
                                        <md-select name="part_types" ng-model="business_rule.part_types" multiple ng-change="handlePartTypeSelection()" required ng-disabled="business_rule.isGlobalRule">
                                            <md-option ng-repeat="partType in PartTypes" value="{{partType.PartTypeName}}" ng-click="partType.PartTypeName === 'All' ? selectAllPartType($event) : null"> {{partType.PartTypeName}} </md-option>
                                        </md-select>
                                        <div class="error-sapce">
                                            <div ng-messages="BRForm.part_types.$error" multiple ng-if='BRForm.part_types.$dirty'>
                                                <div ng-message="required">This is required.</div>
                                            </div>
                                        </div>
                                    </md-input-container>
                                </div>

                                <!-- 9. Source Type -->
                                <div class="col-md-3">
                                    <md-input-container class="md-block">
                                        <label>Source Type</label>
                                        <md-select name="idCustomertype" ng-model="business_rule.idCustomertype" ng-disabled="business_rule.isGlobalRule">
                                            <md-option ng-value="'all'">All</md-option>
                                            <md-option ng-repeat="sourceType in SourceTypes" ng-value="{{sourceType.idCustomertype}}" ng-if="sourceType.Cumstomertype != 'None'"> {{sourceType.Cumstomertype}} </md-option>
                                        </md-select>
                                    </md-input-container>
                                </div>

                                <!-- 10. Material Type -->
                                <div class="col-md-3">
                                    <md-input-container class="md-block">
                                        <label>Material Type</label>
                                        <md-select name="MaterialType" ng-model="business_rule.MaterialType" ng-disabled="business_rule.isGlobalRule">
                                            <md-option value="all">All</md-option>
                                            <md-option ng-repeat="materialType in MaterialTypes" value="{{materialType.MaterialType}}" ng-if="materialType.MaterialType != 'None'"> {{materialType.MaterialType}} </md-option>
                                        </md-select>
                                    </md-input-container>
                                </div>

                                <div class="col-md-3">
                                    <md-switch aria-label="Global Rule" class="md-default" style="margin-top: 20px;" ng-model="business_rule.isGlobalRule" ng-change="setGlobalRule()">
                                        <span>Global Rule</span>
                                    </md-switch>
                                </div>

                            </div>

                        </div>

                    </md-card>

                    <md-card class="no-margin-h">

                        <md-toolbar class="md-table-toolbar md-default">
                            <div class="md-toolbar-tools">
                                <span>Conditions</span>
                            </div>
                        </md-toolbar>
                        <!--condition block start-->
                        <div class="row">
                            <!-- {{business_rule}} -->
                            <div class="col-md-12">
                                <div class="col-md-12">
                                    <md-input-container class="md-block">
                                        <label>Input Result</label>
                                        <md-select name="input_id" ng-required="!business_rule.input_type || business_rule.input_type.length === 0" ng-model="business_rule.input_id" multiple ng-change="updateGroupSelection()">
                                            <div ng-repeat="inputType in inputTypeGroups">
                                                <!-- Group header with checkbox before input_type -->
                                                <md-optgroup>
                                                    <div
                                                        style="padding: 8px 16px; cursor: pointer; background-color: #f5f5f5; margin-bottom: 4px; display: flex; align-items: center; border-left: 3px solid #f44336;"
                                                        ng-click="toggleGroupSelection(inputType)">
                                                        <md-checkbox
                                                            ng-checked="isGroupSelected(inputType)"
                                                            ng-click="toggleGroupSelection(inputType); $event.stopPropagation();"
                                                            aria-label="Select all {{inputType}}">
                                                        </md-checkbox>
                                                        <span style="color: red; font-weight: bold; margin-left: 8px;">{{inputType}}</span>
                                                    </div>
                                                    <!-- Individual options within the group -->
                                                    <md-option
                                                        ng-repeat="ir in groupedInputResults[inputType]"
                                                        value="{{ir.input_id}}"
                                                        style="padding-left: 48px; border-left: 1px solid #e0e0e0; margin-left: 24px; margin-bottom: 2px;">
                                                        {{ir.input}}<span ng-if="ir.workflow_name"> ({{ir.workflow_name}})</span>
                                                    </md-option>
                                                </md-optgroup>
                                            </div>
                                        </md-select>
                                        <div class="error-sapce">
                                            <div ng-messages="BRForm.input_id.$error" multiple ng-if='BRForm.input_id.$dirty'>
                                                <div ng-message="required">This is required.</div>
                                            </div>
                                        </div>
                                    </md-input-container>
                                </div>
                            </div>
                            <div class="col-md-12">

                                <div class="col-md-3">
                                    <md-input-container class="md-block">
                                        <label>Attribute</label>
                                        <md-select name="attribute_id" ng-model="newcondition.attribute_id" ng-change="GetAttributeValues()">
                                            <md-option value="{{attr.attribute_id}}" ng-repeat="attr in Attributes">{{attr.attribute_name}}</md-option>
                                        </md-select>
                                        <div class="error-sapce"></div>
                                    </md-input-container>
                                </div>

                                <div class="col-md-3">
                                    <md-input-container class="md-block">
                                        <label>Operator</label>
                                        <md-select name="operator" ng-model="newcondition.operator">
                                            <!-- <md-option value="==" ng-show="newcondition.attribute_id != 35 && newcondition.attribute_id != 28">Equal To</md-option>
                                            <md-option value="!=" ng-show="newcondition.attribute_id != 35 && newcondition.attribute_id != 28">Not Equal To</md-option> -->

                                            <!-- <md-option value="==" ng-show="newcondition.attribute_id == 35 || newcondition.attribute_id == 15 || newcondition.attribute_id == 16 || newcondition.attribute_id == 1 || newcondition.attribute_id == 11 || newcondition.attribute_id == 12 || newcondition.attribute_id == 18">Equal To</md-option>
                                            <md-option value="!=" ng-show="newcondition.attribute_id == 35 || newcondition.attribute_id == 15 || newcondition.attribute_id == 16 || newcondition.attribute_id == 1 || newcondition.attribute_id == 11 || newcondition.attribute_id == 12 || newcondition.attribute_id == 18">Not Equal To</md-option> -->

                                            <md-option value="==" ng-show="newcondition.attribute_id != 29 && newcondition.attribute_id != 30 && newcondition.attribute_id != 35">Equal To</md-option>
                                            <md-option value="!=" ng-show="newcondition.attribute_id != 29 && newcondition.attribute_id != 30 && newcondition.attribute_id != 35">Not Equal To</md-option>

                                            <!-- <md-option value=">" ng-show="newcondition.attribute_id != 35 && newcondition.attribute_id != 28">Greater Than</md-option>
                                            <md-option value="<" ng-show="newcondition.attribute_id != 35 && newcondition.attribute_id != 28">Less Than</md-option> -->

                                            <md-option value=">" ng-show="newcondition.attribute_id == 29 || newcondition.attribute_id == 30">Greater Than</md-option>
                                            <md-option value="<" ng-show="newcondition.attribute_id == 29 || newcondition.attribute_id == 30">Less Than</md-option>

                                            <!-- <md-option value="contains">Contains</md-option>
                                            <md-option value="not_contains">Not Contains</md-option> -->

                                            <md-option value="contains" ng-show="newcondition.attribute_id == 35 || newcondition.attribute_id == 15 || newcondition.attribute_id == 16">Contains</md-option>
                                            <md-option value="not_contains" ng-show="newcondition.attribute_id == 35 || newcondition.attribute_id == 15 || newcondition.attribute_id == 16">Not Contains</md-option>
                                        </md-select>
                                        <div class="error-sapce"></div>
                                    </md-input-container>
                                </div>

                                <div class="col-md-3">
                                    <md-input-container class="md-block" ng-show="newcondition.attribute_id != '25' && newcondition.attribute_id != '29'">
                                        <label>Value</label>
                                        <md-select name="value" ng-model="newcondition.value" multiple >
                                            <md-option value="{{val.value}}" ng-repeat="val in AttributeValues" ng-if="newcondition.attribute_id != '15' && newcondition.attribute_id != '39'">{{val.value}}</md-option>
                                            <md-option value="{{val.idManufacturer}}" ng-repeat="val in AttributeValues" ng-if="newcondition.attribute_id == '15'">{{val.ManufacturerName}}</md-option>
                                            <md-option value="{{val.COOID}}" ng-repeat="val in AttributeValues" ng-if="newcondition.attribute_id == '39'">{{val.COO}}</md-option>
                                        </md-select>
                                        <div class="error-sapce"></div>
                                    </md-input-container>

                                    <md-input-container class="md-block" ng-show="newcondition.attribute_id == '25' || newcondition.attribute_id == '29'">
                                        <label>Value</label>
                                        <input type="text" name="value" placeholder="Enter Value" ng-model="newcondition.value" ng-maxlength="200">
                                        <div class="error-sapce"></div>
                                    </md-input-container>
                                </div>
                                <div class="clo-md-3">
                                    <button class="md-button md-raised md-default" style="display: flex; margin: 12px;" ng-disabled="newcondition.operator == '' || newcondition.attribute_id == '' || newcondition.value == ''" ng-click="AddCondition()">
                                        <i class="material-icons">add</i> Add Condition
                                    </button>
                                </div>
                            </div>

                            <div class="col-md-12">
                                <div class="col-md-12">
                                    <md-list>
                                        <md-list-item class="ruleliststyle border-radius"  ng-repeat="cond in business_rule.conditions">
                                            <p style="display: flex;">
                                                <!-- <i class="material-icons text-danger mr-5" style="cursor: pointer;">edit</i> -->
                                                {{cond.attribute_name}}
                                                <strong style="margin: 0 6px;" ng-show="cond.operator == '=='" class="text-danger"> Equal To</strong>
                                                <strong style="margin: 0 6px;" ng-show="cond.operator == '!='" class="text-danger"> Not Equal To</strong>

                                                <strong style="margin: 0 6px;" ng-show="cond.operator == '>'" class="text-danger"> Greater Than</strong>
                                                <strong style="margin: 0 6px;" ng-show="cond.operator == '<'" class="text-danger"> Less Than</strong>

                                                <strong style="margin: 0 6px;" ng-show="cond.operator == 'contains'" class="text-danger"> Contains</strong>
                                                <strong style="margin: 0 6px;" ng-show="cond.operator == 'not_contains'" class="text-danger"> Not Contains</strong>
                                                "{{cond.value}}"
                                            </p>
                                            <i class="material-icons edit text-danger" style="cursor: pointer;" ng-click="RemoveCondition($index,$event)">close</i>
                                        </md-list-item>
                                    </md-list>
                                </div>
                            </div>
                        </div>
                        <!--condition block end-->
                    </md-card>

                    <md-card class="no-margin-h pt-5">
                        <md-toolbar class="md-table-toolbar md-default">
                            <div class="md-toolbar-tools">
                                <span>Disposition</span>
                            </div>
                        </md-toolbar>
                        <div class="row">
                            <form name="condition">
                                <div class="col-md-12">
                                    <div class="col-md-3">
                                        <md-input-container class="md-block">
                                            <label>Disposition</label>
                                            <md-select name="disposition_id" ng-model="business_rule.disposition_id" required ng-change="GetSubDispositions()">
                                                <md-option value="{{disp.disposition_id}}" ng-repeat="disp in Dispositions">{{disp.disposition}}</md-option>
                                            </md-select>
                                            <div class="error-sapce">
                                                <div ng-messages="BRForm.disposition_id.$error" multiple ng-if='BRForm.disposition_id.$dirty'>
                                                    <div ng-message="required">This is required.</div>
                                                </div>
                                            </div>
                                        </md-input-container>
                                    </div>
                                    <!-- <div class="col-md-3">
                                        <md-switch aria-label="Primary" class="md-default" style="margin-top: 20px;" ng-model="business_rule.business_SubDispositionExists" ng-change="GetSubDispositions()">
                                            <span>Sub Disposition?</span>
                                        </md-switch>
                                    </div> -->
                                    <div class="col-md-3" ng-if="business_rule.business_SubDispositionExists">
                                        <md-input-container class="md-block">
                                            <label>Choose Sub Disposition</label>
                                            <md-select name="subdisposition" ng-model="business_rule.sub_disposition_id" required>
                                                <md-option value="{{disp.disposition_id}}" ng-repeat="disp in SubDispositions">{{disp.disposition}}</md-option>
                                            </md-select>
                                            <div class="error-sapce"></div>
                                        </md-input-container>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </md-card>

                    <md-card class="no-margin-h pt-5">
                        <div class="row">
                            <form name="condition">
                                <div class="col-md-12">
                                    <div class="btns-row">

                                        <a href="#!/BusinessRuleList" style="text-decoration: none;">
                                            <md-button class="md-button md-raised btn-w-md  md-default">
                                                Cancel
                                            </md-button>
                                        </a>
                                        <md-button class="md-raised btn-w-md md-primary btn-w-md"
                                            data-ng-disabled="BRForm.$invalid || business_rule.busy" ng-click="CreateBusinessRule()">
                                            <span ng-show="! business_rule.busy">Save</span>
                                            <span ng-show="business_rule.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span>
                                        </md-button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </md-card>

                    <!-- Audit Information Card - Only show for existing rules -->
                    <md-card class="no-margin-h pt-5" ng-show="business_rule.rule_id && (business_rule.created_date || business_rule.created_by || business_rule.updated_date || business_rule.updated_by)">
                        <md-toolbar class="md-table-toolbar md-default">
                            <div class="md-toolbar-tools">
                                <span>Audit Information</span>
                            </div>
                        </md-toolbar>
                        <div class="row" style="padding: 20px;">
                            <div class="col-md-12">
                                <!-- Created Information -->
                                <div class="col-md-6" ng-show="business_rule.created_date || business_rule.created_by">
                                    <div style="background-color: #f8f9fa; padding: 15px; border-radius: 4px; border-left: 4px solid #28a745;">
                                        <h4 style="margin-top: 0; color: #28a745; font-size: 16px;">
                                            <i class="material-icons" style="vertical-align: middle; margin-right: 8px;">add_circle</i>
                                            Created
                                        </h4>
                                        <div ng-show="business_rule.created_date" style="margin-bottom: 8px;">
                                            <strong>Date:</strong>
                                            <span>{{business_rule.created_date | date:'MM/dd/yyyy HH:mm'}}</span>
                                        </div>
                                        <div ng-show="business_rule.created_by" style="margin-bottom: 0;">
                                            <strong>By:</strong>
                                            <span>{{business_rule.created_by}}</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Updated Information -->
                                <div class="col-md-6" ng-show="business_rule.updated_date || business_rule.updated_by">
                                    <div style="background-color: #f8f9fa; padding: 15px; border-radius: 4px; border-left: 4px solid #ffc107;">
                                        <h4 style="margin-top: 0; color: #ffc107; font-size: 16px;">
                                            <i class="material-icons" style="vertical-align: middle; margin-right: 8px;">edit</i>
                                            Last Updated
                                        </h4>
                                        <div ng-show="business_rule.updated_date" style="margin-bottom: 8px;">
                                            <strong>Date:</strong>
                                            <span>{{business_rule.updated_date | date:'MM/dd/yyyy HH:mm'}}</span>
                                        </div>
                                        <div ng-show="business_rule.updated_by" style="margin-bottom: 0;">
                                            <strong>By:</strong>
                                            <span>{{business_rule.updated_by}}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </md-card>
                    <!--Create Rules Close-->
                </form>
            </article>
        </div>
    </div>

</div>