<?php
session_start();
include_once("admin.class.php");
class ConsolidationClass extends AdminClass {

    public function GetCustomPalletDetails ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Bin Consolidation')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Bin Consolidation Page';
				return json_encode($json);
			}
			$query = "select * from custompallet where BinName = '".mysqli_real_escape_string($this->connectionlink,$data['BinName'])."'";
            $query = "select c.*,d.disposition,d.eligible_for_bin_consolidation from custompallet c left join disposition d on c.disposition_id = d.disposition_id where BinName = '".mysqli_real_escape_string($this->connectionlink,$data['BinName'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}			
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);
				if($row['StatusID'] != '1') {
					$json['Success'] = false;
					$json['Result'] = 'BIN Status is not Active';
					return json_encode($json);
				}

				if($row['FacilityID'] != $_SESSION['user']['FacilityID']) {
					$json['Success'] = false;
					$json['Result'] = 'BIN Facility is different from Users Facility';
					return json_encode($json);
				}

				if($row['eligible_for_bin_consolidation'] == '0') {
					$json['Success'] = false;
                    $json['Result'] = 'BIN Disposition ('.$row['disposition'].') is not configured for Bin Consolidation';
					return json_encode($json);
				}

				$json['Success'] = true;
				$json['Result'] = $row;
			} else {
				$json['Success'] = false;
				$json['Result'] = "Invalid BIN";
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

    public function AddSerialToBin ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Bin Consolidation')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Bin Consolidation Page';
				return json_encode($json);
			}

            if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Bin Consolidation')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Bin Consolidation Page';
				return json_encode($json);
			}
            
            //Start check CustomPallet Details
            $query = "select c.* from custompallet c where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}			
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$cp = mysqli_fetch_assoc($q);
				if($cp['StatusID'] != '1') {
					$json['Success'] = false;
					$json['Result'] = 'BIN Status is not Active';
					return json_encode($json);
				}

				if($cp['FacilityID'] != $_SESSION['user']['FacilityID']) {
					$json['Success'] = false;
					$json['Result'] = 'BIN Facility is different from Users Facility';
					return json_encode($json);
				}	
				//Start check If BIN has enough space to handle Serial
				if(($cp['MaximumAssets'] - $cp['AssetsCount']) < 1) {
					$json['Success'] = false;
					$json['Result'] = 'BIN has not enough space to handle Serial';
					return json_encode($json);
				}			
			} else {
				$json['Success'] = false;
				$json['Result'] = "Invalid BIN";
                return json_encode($json);
			}
            //End check CustomPallet Details			
            //Start check Serial Details
            $query20 = "select * from asset where SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."' and (StatusID = '1' or StatusID = 9)";
			$q20 = mysqli_query($this->connectionlink,$query20);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$asset = mysqli_fetch_assoc($q20);
				// if($asset['StatusID'] != 1 && $asset['StatusID'] != 9) {
				// 	$json['Success'] = false;
				// 	$json['Result'] = 'Asset Status is not Active';
				// 	return json_encode($json);
				// }

				//Start get asset_cp details

				$query11 = "select c.* from custompallet c where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$asset['CustomPalletID'])."'";
				$q11 = mysqli_query($this->connectionlink,$query11);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}			
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$from_cp = mysqli_fetch_assoc($q11);									
				} else {
					$json['Success'] = false;
					$json['Result'] = "Invalid From BIN";
					return json_encode($json);
				}

				//End get asset_cp details

			} else { //Start check for Server details
				$query20 = "select * from speed_server_recovery where ServerSerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."' and (StatusID = '1' or StatusID = 9)";
				$q20 = mysqli_query($this->connectionlink,$query20);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$asset = mysqli_fetch_assoc($q20);
					//Start get asset_cp details
					$query11 = "select c.* from custompallet c where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$asset['CustomPalletID'])."'";
					$q11 = mysqli_query($this->connectionlink,$query11);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}			
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$from_cp = mysqli_fetch_assoc($q11);									
					} else {
						$json['Success'] = false;
						$json['Result'] = "Invalid From BIN";
						return json_encode($json);
					}

					//End get asset_cp details

				} else { //Start check for Media Details

					$query20 = "select * from speed_media_recovery where MediaSerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."' order by StatusID";
					$q20 = mysqli_query($this->connectionlink,$query20);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$asset = mysqli_fetch_assoc($q20);

						if($asset['StatusID'] == '5') {
							$json['Success'] = false;
							$json['Result'] = 'Media Status is Added to Shipment';
							return json_encode($json);
						}

						if($asset['StatusID'] == '6') {
							$json['Success'] = false;
							$json['Result'] = 'Media Status is Shipped';
							return json_encode($json);
						}

						//Start get asset_cp details
						$query11 = "select c.* from custompallet c where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$asset['CustomPalletID'])."'";
						$q11 = mysqli_query($this->connectionlink,$query11);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);
						}			
						if(mysqli_affected_rows($this->connectionlink) > 0) {
							$from_cp = mysqli_fetch_assoc($q11);									
						} else {
							$json['Success'] = false;
							$json['Result'] = "Invalid From BIN";
							return json_encode($json);
						}

						//End get asset_cp details
					} else {
						$json['Success'] = false;
						$json['Result'] = "Invalid Serial";
						return json_encode($json);
					}

				}

			}
            //End check Serial Details

            //Start check IF Disposition matches
            if($cp['AcceptAllDisposition'] == 0) {//CP Accepts only specific Disposition
                if($cp['disposition_id'] != $asset['disposition_id']) {
                    $json['Success'] = false;
					$json['Result'] = 'Serial Disposition is not matching with BIN Disposition';
					return json_encode($json);
                }
            }
            //End check IF Disposition matches

			//Start insert into bin movement table
			$event_id = rand(1000000000, 9999999999);
			$batch_event_flag = 'N';
			if($asset['AssetScanID'] > 0) {

			
				$query23 = "insert into bin_consolidation_asset_movement (AssetScanID,SerialNumber,FromCustomPalletID,ToCustomPalletID,FacilityID,UniversalModelNumber,part_type,disposition_id,CreatedDate,CreatedBy,MovementType,FromBinName,ToBinName,idManufacturer,idPallet,event_id,batch_event_flag) values ('".mysqli_real_escape_string($this->connectionlink,$asset['AssetScanID'])."','".mysqli_real_escape_string($this->connectionlink,$asset['SerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$asset['CustomPalletID'])."','".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."','".$_SESSION['user']['FacilityID']."','".mysqli_real_escape_string($this->connectionlink,$asset['UniversalModelNumber'])."','".mysqli_real_escape_string($this->connectionlink,$asset['part_type'])."','".mysqli_real_escape_string($this->connectionlink,$asset['disposition_id'])."',NOW(),'".$_SESSION['user']['UserId']."','SINGLE','".mysqli_real_escape_string($this->connectionlink,$from_cp['BinName'])."','".mysqli_real_escape_string($this->connectionlink,$cp['BinName'])."','".mysqli_real_escape_string($this->connectionlink,$asset['idManufacturer'])."','".mysqli_real_escape_string($this->connectionlink,$asset['idPallet'])."','".$event_id."','".$batch_event_flag."')";
				$q23 = mysqli_query($this->connectionlink,$query23);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				//End insert into bin movement table

				//Start update Asset
				$query1 = "update asset set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."',DateUpdated = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."' where AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$asset['AssetScanID'])."'";
				$q1 = mysqli_query($this->connectionlink,$query1);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}			
				//End update Asset

				//Start update Custom Pallet Items
				$query2 = "update custompallet_items set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."' where AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$asset['AssetScanID'])."'";
				$q2 = mysqli_query($this->connectionlink,$query2);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				//End update Custom Pallet Items
			

				//Start update Custom Pallet Counts
				$query3 = "UPDATE `custompallet` SET `AssetsCount`= `AssetsCount` + 1 WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."'";
				$q3 = mysqli_query($this->connectionlink,$query3);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}

				$query4 = "UPDATE `custompallet` SET `AssetsCount`= `AssetsCount` - 1 WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$asset['CustomPalletID'])."'";
				$q4 = mysqli_query($this->connectionlink,$query4);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				//End update Custom Pallet Counts

				//Insert into Asset Tracking
				$desc = "Asset Moved from BIN, (BIN ID : ".$from_cp['BinName'].") to BIN, (BIN ID : ".$cp['BinName'].") in Bin Consolidation Screen";
				$query3 = "insert into asset_tracking (AssetScanID,Action,Description,UniqueID,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$asset['AssetScanID'])."','".mysqli_real_escape_string($this->connectionlink,$desc)."','','',NOW(),'".$_SESSION['user']['UserId']."')";
				$q3 = mysqli_query($this->connectionlink,$query3);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				//End Inserting into Asset Tracking
			} else if($asset['ServerID'] > 0) {

			
				$query23 = "insert into bin_consolidation_asset_movement (ServerID,SerialNumber,FromCustomPalletID,ToCustomPalletID,FacilityID,UniversalModelNumber,part_type,disposition_id,CreatedDate,CreatedBy,MovementType,FromBinName,ToBinName,idPallet,event_id,batch_event_flag) values ('".mysqli_real_escape_string($this->connectionlink,$asset['ServerID'])."','".mysqli_real_escape_string($this->connectionlink,$asset['ServerSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$asset['CustomPalletID'])."','".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."','".$_SESSION['user']['FacilityID']."','".mysqli_real_escape_string($this->connectionlink,$asset['MPN'])."','".mysqli_real_escape_string($this->connectionlink,$asset['Type'])."','".mysqli_real_escape_string($this->connectionlink,$asset['disposition_id'])."',NOW(),'".$_SESSION['user']['UserId']."','SINGLE','".mysqli_real_escape_string($this->connectionlink,$from_cp['BinName'])."','".mysqli_real_escape_string($this->connectionlink,$cp['BinName'])."','".mysqli_real_escape_string($this->connectionlink,$asset['idPallet'])."','".$event_id."','".$batch_event_flag."')";
				$q23 = mysqli_query($this->connectionlink,$query23);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				//End insert into bin movement table

				//Start update Asset
				$query1 = "update speed_server_recovery set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."' where ServerID = '".mysqli_real_escape_string($this->connectionlink,$asset['ServerID'])."'";
				$q1 = mysqli_query($this->connectionlink,$query1);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}			
				//End update Asset

				//Start update Custom Pallet Items
				$query2 = "update custompallet_items set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."' where ServerID = '".mysqli_real_escape_string($this->connectionlink,$asset['ServerID'])."'";
				$q2 = mysqli_query($this->connectionlink,$query2);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				//End update Custom Pallet Items
			

				//Start update Custom Pallet Counts
				$query3 = "UPDATE `custompallet` SET `AssetsCount`= `AssetsCount` + 1 WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."'";
				$q3 = mysqli_query($this->connectionlink,$query3);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}

				$query4 = "UPDATE `custompallet` SET `AssetsCount`= `AssetsCount` - 1 WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$asset['CustomPalletID'])."'";
				$q4 = mysqli_query($this->connectionlink,$query4);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				//End update Custom Pallet Counts

				//Insert into Asset Tracking
				$desc = "Serial Moved from BIN, (BIN ID : ".$from_cp['BinName'].") to BIN, (BIN ID : ".$cp['BinName'].") in Bin Consolidation Screen";
				$query3 = "insert into speed_server_recovery_tracking (ServerID,Action,Description,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$asset['ServerID'])."','".mysqli_real_escape_string($this->connectionlink,$desc)."','',NOW(),'".$_SESSION['user']['UserId']."')";
				$q3 = mysqli_query($this->connectionlink,$query3);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				//End Inserting into Asset Tracking
			} else if($asset['MediaID'] > 0) {

			
				$query23 = "insert into bin_consolidation_asset_movement (MediaID,SerialNumber,FromCustomPalletID,ToCustomPalletID,FacilityID,UniversalModelNumber,part_type,disposition_id,CreatedDate,CreatedBy,MovementType,FromBinName,ToBinName,idPallet,event_id,batch_event_flag) values ('".mysqli_real_escape_string($this->connectionlink,$asset['MediaID'])."','".mysqli_real_escape_string($this->connectionlink,$asset['MediaSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$asset['CustomPalletID'])."','".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."','".$_SESSION['user']['FacilityID']."','".mysqli_real_escape_string($this->connectionlink,$asset['MediaMPN'])."','".mysqli_real_escape_string($this->connectionlink,$asset['MediaType'])."','".mysqli_real_escape_string($this->connectionlink,$asset['disposition_id'])."',NOW(),'".$_SESSION['user']['UserId']."','SINGLE','".mysqli_real_escape_string($this->connectionlink,$from_cp['BinName'])."','".mysqli_real_escape_string($this->connectionlink,$cp['BinName'])."','".mysqli_real_escape_string($this->connectionlink,$asset['idPallet'])."','".$event_id."','".$batch_event_flag."')";
				$q23 = mysqli_query($this->connectionlink,$query23);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				//End insert into bin movement table

				//Start update Asset
				$query1 = "update speed_media_recovery set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."' where MediaID = '".mysqli_real_escape_string($this->connectionlink,$asset['MediaID'])."'";
				$q1 = mysqli_query($this->connectionlink,$query1);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}			
				//End update Asset

				//Start update Custom Pallet Items
				$query2 = "update custompallet_items set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."' where MediaID = '".mysqli_real_escape_string($this->connectionlink,$asset['MediaID'])."'";
				$q2 = mysqli_query($this->connectionlink,$query2);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				//End update Custom Pallet Items
			

				//Start update Custom Pallet Counts
				$query3 = "UPDATE `custompallet` SET `AssetsCount`= `AssetsCount` + 1 WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."'";
				$q3 = mysqli_query($this->connectionlink,$query3);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}

				$query4 = "UPDATE `custompallet` SET `AssetsCount`= `AssetsCount` - 1 WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$asset['CustomPalletID'])."'";
				$q4 = mysqli_query($this->connectionlink,$query4);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				//End update Custom Pallet Counts

				//Insert into Asset Tracking
				$desc = "Serial Moved from BIN, (BIN ID : ".$from_cp['BinName'].") to BIN, (BIN ID : ".$cp['BinName'].") in Bin Consolidation Screen";
				$query3 = "insert into speed_media_recovery_tracking (MediaID,Action,Description,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$asset['MediaID'])."','".mysqli_real_escape_string($this->connectionlink,$desc)."','',NOW(),'".$_SESSION['user']['UserId']."')";
				$q3 = mysqli_query($this->connectionlink,$query3);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				//End Inserting into Asset Tracking
			}

            $json['Success'] = true;
            $json['Result'] = 'Serial Moved to BIN';
            return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


    public function CloseBIN ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			// if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Bin Consolidation')) {
			// 	$json['Success'] = false;
			// 	$json['Result'] = 'No Access to Bin Consolidation Page';
			// 	return json_encode($json);
			// }

            // if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Bin Consolidation')) {
			// 	$json['Success'] = false;
			// 	$json['Result'] = 'You have Read only Access to Bin Consolidation Page';
			// 	return json_encode($json);
			// }			
            $query = "select c.* from custompallet c where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}			
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);
                if($row['AssetsCount'] > 0) {
                    $json['Success'] = false;
					$json['Result'] = $row['AssetsCount'].' Serials exist in the BIN, Only BINs with no items can be closed';
					return json_encode($json);
                }
				if($row['StatusID'] == '3') {
					$json['Success'] = false;
					$json['Result'] = 'BIN is already Closed';
					return json_encode($json);
				}

				if($row['FacilityID'] != $_SESSION['user']['FacilityID']) {
					$json['Success'] = false;
					$json['Result'] = 'BIN Facility is different from Users Facility';
					return json_encode($json);
				}

                //Start Close BIN
                $query1 = "update custompallet set StatusID = 3 where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."' ";
                $q1 = mysqli_query($this->connectionlink,$query1);
                if(mysqli_error($this->connectionlink)) {
                    $json['Success'] = false;
                    $json['Result'] = mysqli_error($this->connectionlink);
                    return json_encode($json);
                }
                //End CLose BIN

				$json['Success'] = true;
				$json['Result'] = 'BIN Closed';
                return json_encode($json);
			} else {
				$json['Success'] = false;
				$json['Result'] = "Invalid BIN";
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


    public function ConsolidateBIN ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			// if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Bin Consolidation')) {
			// 	$json['Success'] = false;
			// 	$json['Result'] = 'No Access to Bin Consolidation Page';
			// 	return json_encode($json);
			// }

            // if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Bin Consolidation')) {
			// 	$json['Success'] = false;
			// 	$json['Result'] = 'You have Read only Access to Bin Consolidation Page';
			// 	return json_encode($json);
			// }			
            
             //Start check CustomPallet Details
             $query = "select c.* from custompallet c where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['FromCustomPalletID'])."'";
             $q = mysqli_query($this->connectionlink,$query);
             if(mysqli_error($this->connectionlink)) {
                 $json['Success'] = false;
                 $json['Result'] = mysqli_error($this->connectionlink);
                 return json_encode($json);
             }			
             if(mysqli_affected_rows($this->connectionlink) > 0) {
                 $from_cp = mysqli_fetch_assoc($q);
                 if($from_cp['StatusID'] != '1') {
                     $json['Success'] = false;
                     $json['Result'] = 'From BIN Status is not Active';
                     return json_encode($json);
                 }
 
                 if($from_cp['FacilityID'] != $_SESSION['user']['FacilityID']) {
                     $json['Success'] = false;
                     $json['Result'] = 'From BIN Facility is different from Users Facility';
                     return json_encode($json);
                 }				 
				 if($from_cp['AssetsCount'] == 0) {
					$json['Success'] = false;
					$json['Result'] = 'From BIN has no Serials';
					return json_encode($json);					
				 }
             } else {
                 $json['Success'] = false;
                 $json['Result'] = "Invalid From BIN";
                 return json_encode($json);
             }


             $query = "select c.* from custompallet c where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['ToCustomPalletID'])."'";
             $q = mysqli_query($this->connectionlink,$query);
             if(mysqli_error($this->connectionlink)) {
                 $json['Success'] = false;
                 $json['Result'] = mysqli_error($this->connectionlink);
                 return json_encode($json);
             }			
             if(mysqli_affected_rows($this->connectionlink) > 0) {
                 $to_cp = mysqli_fetch_assoc($q);
                 if($to_cp['StatusID'] != '1') {
                     $json['Success'] = false;
                     $json['Result'] = 'To BIN Status is not Active';
                     return json_encode($json);
                 }
 
                 if($to_cp['FacilityID'] != $_SESSION['user']['FacilityID']) {
                     $json['Success'] = false;
                     $json['Result'] = 'To BIN Facility is different from Users Facility';
                     return json_encode($json);
                 }				
            } else {
                 $json['Success'] = false;
                 $json['Result'] = "Invalid To BIN";
                 return json_encode($json);
            }
             //End check CustomPallet Details

			 //Start check if ToBin has enough space to handle FromBin Serials
			 if(($to_cp['MaximumAssets'] - $to_cp['AssetsCount']) < $from_cp['AssetsCount']) {
				$json['Success'] = false;
				$json['Result'] = 'To BIN has not enough space to handle From BIN Serials';
				return json_encode($json);
			 }


            //Start check IF BIN Dispositions matches
            if($to_cp['AcceptAllDisposition'] == 0) {
                if($to_cp['disposition_id'] != $from_cp['disposition_id']) {
                    $json['Success'] = false;
					$json['Result'] = "Dispositions of the BINs are different and 'Accept All Disposition' is not enabled for To BIN";
					return json_encode($json);
                }
            }
            //End check IF BIN Dispositions matches


            //Start consolidating BINs

            //Insert into Asset Tracking
			$desc = "Asset Moved from BIN, (BIN ID : ".$from_cp['BinName'].") to BIN, (BIN ID : ".$to_cp['BinName'].") in Bin Consolidation Screen";
			//$query3 = "insert into asset_tracking (AssetScanID,Action,Description,UniqueID,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$asset['AssetScanID'])."','".mysqli_real_escape_string($this->connectionlink,$desc)."','','',NOW(),'".$_SESSION['user']['UserId']."')";
            $query3 = "insert into asset_tracking (AssetScanID,Action,Description,UniqueID,CreatedDate,CreatedBy) 
            select AssetScanID,'".mysqli_real_escape_string($this->connectionlink,$desc)."','','',NOW(),'".$_SESSION['user']['UserId']."' from custompallet_items where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$from_cp['CustomPalletID'])."' ";
			$q3 = mysqli_query($this->connectionlink,$query3);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			//End Inserting into Asset Tracking


			//Start insert into BIN movement table
			$event_id = rand(1000000000, 9999999999);
			$batch_event_flag = 'Y';
			$query23 = "insert into bin_consolidation_asset_movement (AssetScanID,SerialNumber,FromCustomPalletID,ToCustomPalletID,FacilityID,UniversalModelNumber,part_type,disposition_id,CreatedDate,CreatedBy,MovementType,FromBinName,ToBinName,idManufacturer,idPallet,event_id,batch_event_flag) 
			select AssetScanID,SerialNumber,'".$from_cp['CustomPalletID']."','".$to_cp['CustomPalletID']."','".$_SESSION['user']['FacilityID']."',UniversalModelNumber,part_type,disposition_id,NOW(),'".$_SESSION['user']['UserId']."','BULK Aset Movement','".$from_cp['BinName']."','".$to_cp['BinName']."',idManufacturer,idPallet,'".$event_id."','".$batch_event_flag."' from asset where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$from_cp['CustomPalletID'])."' 
			";
			$q23 = mysqli_query($this->connectionlink,$query23);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			//End insert into BIN movement table



            //Start update Asset
			$query1 = "update asset set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."',DateUpdated = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."' where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$from_cp['CustomPalletID'])."'";
			$q1 = mysqli_query($this->connectionlink,$query1);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}			
			//End update Asset




			//Insert into SPEED SERVER Tracking
			$desc = "Serial Moved from BIN, (BIN ID : ".$from_cp['BinName'].") to BIN, (BIN ID : ".$to_cp['BinName'].") in Bin Consolidation Screen";			
            $query3 = "insert into speed_server_recovery_tracking (ServerID,ServerSerialNumber,Type,idPallet,Action,Description,CreatedDate,CreatedBy)
            select ServerID,ServerSerialNumber,Type,idPallet,'".mysqli_real_escape_string($this->connectionlink,$desc)."','',NOW(),'".$_SESSION['user']['UserId']."' from speed_server_recovery where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$from_cp['CustomPalletID'])."' ";
			$q3 = mysqli_query($this->connectionlink,$query3);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			//End Inserting into SPEED SERVER Tracking


			//Start insert into BIN movement table			
			$query23 = "insert into bin_consolidation_asset_movement (ServerID,SerialNumber,FromCustomPalletID,ToCustomPalletID,FacilityID,UniversalModelNumber,part_type,disposition_id,CreatedDate,CreatedBy,MovementType,FromBinName,ToBinName,idPallet,event_id,batch_event_flag) 
			select ServerID,ServerSerialNumber,'".$from_cp['CustomPalletID']."','".$to_cp['CustomPalletID']."','".$_SESSION['user']['FacilityID']."',MPN,Type,disposition_id,NOW(),'".$_SESSION['user']['UserId']."','Bin Consolidation','".$from_cp['BinName']."','".$to_cp['BinName']."',idPallet,'".$event_id."','".$batch_event_flag."' from speed_server_recovery where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$from_cp['CustomPalletID'])."' 
			";
			$q23 = mysqli_query($this->connectionlink,$query23);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			//End insert into BIN movement table



            //Start update servers/switches
			$query1 = "update speed_server_recovery set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."' where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$from_cp['CustomPalletID'])."'";
			$q1 = mysqli_query($this->connectionlink,$query1);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}			
			//End update servers/switches




			//Insert into SPEED Media Tracking
			$desc = "Serial Moved from BIN, (BIN ID : ".$from_cp['BinName'].") to BIN, (BIN ID : ".$to_cp['BinName'].") in Bin Consolidation Screen";			
            $query3 = "insert into speed_media_recovery_tracking (MediaID,MediaSerialNumber,MediaType,idPallet,Action,Description,CreatedDate,CreatedBy)
            select MediaID,MediaSerialNumber,MediaType,idPallet,'".mysqli_real_escape_string($this->connectionlink,$desc)."','',NOW(),'".$_SESSION['user']['UserId']."' from speed_media_recovery where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$from_cp['CustomPalletID'])."' ";
			$q3 = mysqli_query($this->connectionlink,$query3);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			//End Inserting into SPEED Media Tracking


			//Start insert into BIN movement table			
			$query23 = "insert into bin_consolidation_asset_movement (MediaID,SerialNumber,FromCustomPalletID,ToCustomPalletID,FacilityID,UniversalModelNumber,part_type,disposition_id,CreatedDate,CreatedBy,MovementType,FromBinName,ToBinName,idPallet,event_id,batch_event_flag) 
			select MediaID,MediaSerialNumber,'".$from_cp['CustomPalletID']."','".$to_cp['CustomPalletID']."','".$_SESSION['user']['FacilityID']."',MediaMPN,MediaType,disposition_id,NOW(),'".$_SESSION['user']['UserId']."','Bin Consolidation','".$from_cp['BinName']."','".$to_cp['BinName']."',idPallet,'".$event_id."','".$batch_event_flag."' from speed_media_recovery where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$from_cp['CustomPalletID'])."' 
			";
			$q23 = mysqli_query($this->connectionlink,$query23);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			//End insert into BIN movement table



            //Start update servers/switches
			$query1 = "update speed_media_recovery set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."' where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$from_cp['CustomPalletID'])."'";
			$q1 = mysqli_query($this->connectionlink,$query1);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}			
			//End update servers/switches





			//Start update Custom Pallet Items
			$query2 = "update custompallet_items set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."' where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$from_cp['CustomPalletID'])."'";
			$q2 = mysqli_query($this->connectionlink,$query2);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			//End update Custom Pallet Items

			//Start update Custom Pallet Counts
            $query33 = "select count(*) from custompallet_items where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."' ";
            $q33 = mysqli_query($this->connectionlink,$query33);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
            if(mysqli_affected_rows($this->connectionlink) > 0) {
                $row33 = mysqli_fetch_assoc($q33);
                $query3 = "UPDATE `custompallet` SET `AssetsCount`= '".mysqli_real_escape_string($this->connectionlink,$row33['count(*)'])."' WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."'";
                $q3 = mysqli_query($this->connectionlink,$query3);
                if(mysqli_error($this->connectionlink)) {
                    $json['Success'] = false;
                    $json['Result'] = mysqli_error($this->connectionlink);
                    return json_encode($json);
                }
            }			

			$query4 = "UPDATE `custompallet` SET `AssetsCount`= 0 WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$from_cp['CustomPalletID'])."'";
			$q4 = mysqli_query($this->connectionlink,$query4);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			//End update Custom Pallet Counts


            //End consolidating BINs

            $json['Success'] = true;
            $json['Result'] = 'BIN Consolidation Success';
            return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetPalletDetails ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			// if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Container Consolidation')) {
			// 	$json['Success'] = false;
			// 	$json['Result'] = 'No Access to Container Consolidation Page';
			// 	return json_encode($json);
			// }			
            $query = "select * from pallets where idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['BinName'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}			
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);
				if($row['status'] != '1') {
					$json['Success'] = false;
					$json['Result'] = 'Container Status is not Active';
					return json_encode($json);
				}

				if($row['Received'] == '0') {
					$json['Success'] = false;
					$json['Result'] = 'Container not received';
					return json_encode($json);
				}

				if($row['PalletFacilityID'] != $_SESSION['user']['FacilityID']) {
					$json['Success'] = false;
					$json['Result'] = 'Container Facility is different from Users Facility';
					return json_encode($json);
				}

				//Start get pending assets count
				$query2 = "select count(*) from asn_assets where idPallet = '".$row['idPallet']."' and isnull(AssetScanID)";
				$q2 = mysqli_query($this->connectionlink,$query2);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row2 = mysqli_fetch_assoc($q2);
					$row['AssetsCount'] = $row2['count(*)'];
				} else {
					$row['AssetsCount'] = 0;
				}
				//End get pending assets count

				$json['Success'] = true;
				$json['Result'] = $row;
			} else {
				$json['Success'] = false;
				$json['Result'] = "Invalid Container";
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}



	public function ConsolidateContainer ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Container Consolidation')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Container Consolidation Page';
				return json_encode($json);
			}

            if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Container Consolidation')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Container Consolidation Page';
				return json_encode($json);
			}			
            
             //Start check CustomPallet Details
             $query = "select c.* from pallets c where idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['FromCustomPalletID'])."'";
             $q = mysqli_query($this->connectionlink,$query);
             if(mysqli_error($this->connectionlink)) {
                 $json['Success'] = false;
                 $json['Result'] = mysqli_error($this->connectionlink);
                 return json_encode($json);
             }			
             if(mysqli_affected_rows($this->connectionlink) > 0) {
                 $from_cp = mysqli_fetch_assoc($q);
                 if($from_cp['status'] != '1') {
                     $json['Success'] = false;
                     $json['Result'] = 'From Container Status is not Active';
                     return json_encode($json);
                 }
 
                 if($from_cp['PalletFacilityID'] != $_SESSION['user']['FacilityID']) {
                     $json['Success'] = false;
                     $json['Result'] = 'From Container Facility is different from Users Facility';
                     return json_encode($json);
                 }	
				 
				if($from_cp['Received'] == '0') {
					$json['Success'] = false;
					$json['Result'] = 'From Container not received';
					return json_encode($json);
				}
				 
             } else {
                 $json['Success'] = false;
                 $json['Result'] = "Invalid From Container";
                 return json_encode($json);
             }


             $query = "select c.* from pallets c where idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['ToCustomPalletID'])."'";
             $q = mysqli_query($this->connectionlink,$query);
             if(mysqli_error($this->connectionlink)) {
                 $json['Success'] = false;
                 $json['Result'] = mysqli_error($this->connectionlink);
                 return json_encode($json);
             }			
             if(mysqli_affected_rows($this->connectionlink) > 0) {
				$to_cp = mysqli_fetch_assoc($q);
				if($to_cp['status'] != '1') {
					$json['Success'] = false;
					$json['Result'] = 'To Container Status is not Active';
					return json_encode($json);
				}
 
				if($to_cp['PalletFacilityID'] != $_SESSION['user']['FacilityID']) {
					$json['Success'] = false;
					$json['Result'] = 'To Container Facility is different from Users Facility';
					return json_encode($json);
                }	
				 
				if($to_cp['Received'] == '0') {
					$json['Success'] = false;
					$json['Result'] = 'To Container not received';
					return json_encode($json);
				}

				//Start get pallet_items_id for to_cp
				$query221 = "select id from pallet_items where palletId = '".mysqli_real_escape_string($this->connectionlink,$to_cp['idPallet'])."' ";
				$q221 = mysqli_query($this->connectionlink,$query221);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row221 = mysqli_fetch_assoc($q221);
					$to_cp['pallet_items_id'] = $row221['id'];
				} else {
					$to_cp['pallet_items_id'] = 0;
				}
				//End et pallet_items_id for to_cp
            } else {
                 $json['Success'] = false;
                 $json['Result'] = "Invalid To Container";
                 return json_encode($json);
            }
             //End check CustomPallet Details


            //Start consolidating Containers


			//Insert into History
			
            $query3 = "insert into asn_assets_changed_pallets (SerialNumber,ChangedFromPalletID,ChangedToPalletID,CreatedDate,CreatedBy,Comments) 
            select SerialNumber,'".mysqli_real_escape_string($this->connectionlink,$from_cp['idPallet'])."','".mysqli_real_escape_string($this->connectionlink,$to_cp['idPallet'])."',NOW(),'".$_SESSION['user']['UserId']."','Container Consolidation' from asn_assets where idPallet = '".mysqli_real_escape_string($this->connectionlink,$from_cp['idPallet'])."' and isnull(AssetScanID) ";
			$q3 = mysqli_query($this->connectionlink,$query3);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			//End Inserting into History

            //Start update Asset
			//$query1 = "update asn_assets set idPallet = '".mysqli_real_escape_string($this->connectionlink,$to_cp['idPallet'])."',LoadId = '".mysqli_real_escape_string($this->connectionlink,$to_cp['LoadId'])."',PalletItemsID = NULL where idPallet = '".mysqli_real_escape_string($this->connectionlink,$from_cp['idPallet'])."' and isnull(AssetScanID)";
			$query1 = "update asn_assets set idPallet = '".mysqli_real_escape_string($this->connectionlink,$to_cp['idPallet'])."',LoadId = '".mysqli_real_escape_string($this->connectionlink,$to_cp['LoadId'])."',PalletItemsID = '".mysqli_real_escape_string($this->connectionlink,$to_cp['pallet_items_id'])."' where idPallet = '".mysqli_real_escape_string($this->connectionlink,$from_cp['idPallet'])."' and isnull(AssetScanID)";
			$q1 = mysqli_query($this->connectionlink,$query1);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}			
			//End update Asset

            //End consolidating Containers

			//Start close From Container
			if($data['CloseContainer'] == '1') {
				$query16 = "update pallets set status = 3,UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',WarehouseLocationId = NULL where idPallet = '".mysqli_real_escape_string($this->connectionlink,$from_cp['idPallet'])."' ";
				$q16 = mysqli_query($this->connectionlink,$query16);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}

				//Start Unlock Pallet Location			
				$sqllocold = "UPDATE `location` SET `Locked` = '2', `currentItemType` = '', `currentItemID` = '' WHERE `LocationID` = '".$from_cp['WarehouseLocationId']."'";
				$querylocold = mysqli_query($this->connectionlink,$sqllocold);			
				$record_location = $this->RecordLocationHistory('Container',$from_cp['idPallet'],$from_cp['WarehouseLocationId'],'0','Container Closed in Container Consolidation Screen');
				//End Unlock Pallet Location

				$query17 = "insert into pallet_tracking (idPallet,`Action`,Description,UniqueID,CreatedDate,CreatedBy,`Table`,ReferenceID,RequestName) values ('".mysqli_real_escape_string($this->connectionlink,$from_cp['idPallet'])."','Container Closed in Container Consolidation','','',NOW(),'".$_SESSION['user']['UserId']."','','','')";
				$q17 = mysqli_query($this->connectionlink,$query17);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = $query2;
					return json_encode($json);
				}
				$json['PalletClosed'] = '1';
			}
			//End close From Container

            $json['Success'] = true;
            $json['Result'] = 'Consolidation Consolidation Success';
            return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function AddSerialToContainer ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			// if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Container Consolidation')) {
			// 	$json['Success'] = false;
			// 	$json['Result'] = 'No Access to Container Consolidation Page';
			// 	return json_encode($json);
			// }

            // if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Container Consolidation')) {
			// 	$json['Success'] = false;
			// 	$json['Result'] = 'You have Read only Access to Container Consolidation Page';
			// 	return json_encode($json);
			// }
            
            //Start check CustomPallet Details
			$query = "select c.* from pallets c where idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}			
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$cp = mysqli_fetch_assoc($q);
				if($cp['status'] != '1') {
					$json['Success'] = false;
					$json['Result'] = 'From Container Status is not Active';
					return json_encode($json);
				}

				if($cp['PalletFacilityID'] != $_SESSION['user']['FacilityID']) {
					$json['Success'] = false;
					$json['Result'] = 'From Container Facility is different from Users Facility';
					return json_encode($json);
				}	
				
			if($cp['Received'] == '0') {
				$json['Success'] = false;
				$json['Result'] = 'From Container not received';
				return json_encode($json);
			}
				
			} else {
				$json['Success'] = false;
				$json['Result'] = "Invalid From Container";
				return json_encode($json);
			}
            //End check CustomPallet Details

            //Start check Serial Details
            $query20 = "select * from asn_assets where SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."'";
			$q20 = mysqli_query($this->connectionlink,$query20);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$asset = mysqli_fetch_assoc($q20);
				if($asset['AssetScanID'] > 0) {
					$json['Success'] = false;
					$json['Result'] = 'Serial Moved to BIN';
					return json_encode($json);
				}
				
				if($asset['idPallet'] == $data['CustomPalletID']) {
					$json['Success'] = false;
					$json['Result'] = 'Serial already in the Container '.$data['CustomPalletID'];
					return json_encode($json);
				}
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Invalid Serial';
				return json_encode($json);
			}
            //End check Serial Details

            //Start update Asset
			$query1 = "update asn_assets set idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."',LoadId = '".mysqli_real_escape_string($this->connectionlink,$cp['LoadId'])."' where ID = '".mysqli_real_escape_string($this->connectionlink,$asset['ID'])."'";
			$q1 = mysqli_query($this->connectionlink,$query1);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}			
			//End update Asset

			//Start update Custom Pallet Items			
			$query2 = "insert into asn_assets_changed_pallets (SerialNumber,ChangedFromPalletID,ChangedToPalletID,CreatedDate,CreatedBy,Comments) values ('".mysqli_real_escape_string($this->connectionlink,$asset['SerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$asset['idPallet'])."','".mysqli_real_escape_string($this->connectionlink,$cp['idPallet'])."',NOW(),'".$_SESSION['user']['UserId']."','Serial transfer in Container Consolidation')";
			$q2 = mysqli_query($this->connectionlink,$query2);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			//End update Custom Pallet Items

            $json['Success'] = true;
            $json['Result'] = 'Serial Moved to Container';
            return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function RecordLocationHistory($itemtype,$itemid,$FromLocationID,$ToLocationID,$description) {
		$query = "insert into location_history (ItemType,ItemID,FromLocationID,ToLocationID,CreatedDate,CreatedBy,Description) values ('".mysqli_real_escape_string($this->connectionlink,$itemtype)."','".mysqli_real_escape_string($this->connectionlink,$itemid)."','".mysqli_real_escape_string($this->connectionlink,$FromLocationID)."','".mysqli_real_escape_string($this->connectionlink,$ToLocationID)."',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$description)."')";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {			
			return mysqli_error($this->connectionlink);	
		} else {
			return 1;
		}
	}	


	public function GetCustomPalletDetailsForServerSwitch ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Server/Switch Consolidation')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Server/Switch Consolidation Page';
				return json_encode($json);
			}			
            $query = "select c.*,d.disposition from custompallet c left join disposition d on c.disposition_id = d.disposition_id where BinName = '".mysqli_real_escape_string($this->connectionlink,$data['BinName'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}			
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);
				if($row['StatusID'] != '1') {
					$json['Success'] = false;
					$json['Result'] = 'BIN Status is not Active';
					return json_encode($json);
				}

				if($row['FacilityID'] != $_SESSION['user']['FacilityID']) {
					$json['Success'] = false;
					$json['Result'] = 'BIN Facility is different from Users Facility';
					return json_encode($json);
				}

				$json['Success'] = true;
				$json['Result'] = $row;
			} else {
				$json['Success'] = false;
				$json['Result'] = "Invalid BIN";
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function AddServerSwitchToBin ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Server/Switch Consolidation')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Server/Switch Consolidation Page';
				return json_encode($json);
			}

            if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Server/Switch Consolidation')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Server/Switch Consolidation Page';
				return json_encode($json);
			}
            
            //Start check CustomPallet Details
            $query = "select c.* from custompallet c where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}			
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$cp = mysqli_fetch_assoc($q);
				if($cp['StatusID'] != '1') {
					$json['Success'] = false;
					$json['Result'] = 'BIN Status is not Active';
					return json_encode($json);
				}

				if($cp['FacilityID'] != $_SESSION['user']['FacilityID']) {
					$json['Success'] = false;
					$json['Result'] = 'BIN Facility is different from Users Facility';
					return json_encode($json);
				}				
			} else {
				$json['Success'] = false;
				$json['Result'] = "Invalid BIN";
                return json_encode($json);
			}
            //End check CustomPallet Details

            //Start check Serial Details
            $query20 = "select * from speed_server_recovery where ServerSerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."'";
			$q20 = mysqli_query($this->connectionlink,$query20);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$asset = mysqli_fetch_assoc($q20);
				if($asset['StatusID'] != 1 && $asset['StatusID'] != 9) {
					$json['Success'] = false;
					$json['Result'] = $asset['Type'].' Status is not Active';
					return json_encode($json);
				}

				//Start get asset_cp details

				$query11 = "select c.* from custompallet c where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$asset['CustomPalletID'])."'";
				$q11 = mysqli_query($this->connectionlink,$query11);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}			
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$from_cp = mysqli_fetch_assoc($q11);									
				} else {
					$json['Success'] = false;
					$json['Result'] = "Invalid From BIN";
					return json_encode($json);
				}

				//End get asset_cp details

			} else {
				$json['Success'] = false;
				$json['Result'] = 'Invalid Serial';
				return json_encode($json);
			}
            //End check Serial Details

            //Start check IF Disposition matches
            if($cp['AcceptAllDisposition'] == 0) {//CP Accepts only specific Disposition
                if($cp['disposition_id'] != $asset['disposition_id']) {
                    $json['Success'] = false;
					$json['Result'] = $asset['Type'].' Disposition is not matching with BIN Disposition';
					return json_encode($json);
                }
            }
            //End check IF Disposition matches

			//Start insert into bin movement table
			$event_id = rand(1000000000, 9999999999);
			$batch_event_flag = 'N';
			$query23 = "insert into bin_consolidation_asset_movement (ServerID,SerialNumber,FromCustomPalletID,ToCustomPalletID,FacilityID,UniversalModelNumber,part_type,disposition_id,CreatedDate,CreatedBy,MovementType,FromBinName,ToBinName,idPallet,event_id,batch_event_flag) values ('".mysqli_real_escape_string($this->connectionlink,$asset['ServerID'])."','".mysqli_real_escape_string($this->connectionlink,$asset['ServerSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$asset['CustomPalletID'])."','".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."','".$_SESSION['user']['FacilityID']."','".mysqli_real_escape_string($this->connectionlink,$asset['MPN'])."','".mysqli_real_escape_string($this->connectionlink,$asset['Type'])."','".mysqli_real_escape_string($this->connectionlink,$asset['disposition_id'])."',NOW(),'".$_SESSION['user']['UserId']."','SINGLE','".mysqli_real_escape_string($this->connectionlink,$from_cp['BinName'])."','".mysqli_real_escape_string($this->connectionlink,$cp['BinName'])."','".mysqli_real_escape_string($this->connectionlink,$asset['idPallet'])."','".$event_id."','".$batch_event_flag."')";
			$q23 = mysqli_query($this->connectionlink,$query23);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			//End insert into bin movement table

            //Start update Asset
			$query1 = "update speed_server_recovery set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."' where ServerID = '".mysqli_real_escape_string($this->connectionlink,$asset['ServerID'])."'";
			$q1 = mysqli_query($this->connectionlink,$query1);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}			
			//End update Asset

			//Start update Custom Pallet Items
			$query2 = "update custompallet_items set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."' where ServerID = '".mysqli_real_escape_string($this->connectionlink,$asset['ServerID'])."'";
			$q2 = mysqli_query($this->connectionlink,$query2);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			//End update Custom Pallet Items

			//Start update Custom Pallet Counts
			$query3 = "UPDATE `custompallet` SET `AssetsCount`= `AssetsCount` + 1 WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."'";
			$q3 = mysqli_query($this->connectionlink,$query3);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			$query4 = "UPDATE `custompallet` SET `AssetsCount`= `AssetsCount` - 1 WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$asset['CustomPalletID'])."'";
			$q4 = mysqli_query($this->connectionlink,$query4);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			//End update Custom Pallet Counts

			//Insert into Asset Tracking
			$desc = $asset['Type']." Moved from BIN, (BIN ID : ".$from_cp['BinName'].") to BIN, (BIN ID : ".$cp['BinName'].") in Server/Switch Consolidation Screen";
			$query3 = "insert into speed_server_recovery_tracking (ServerID,ServerSerialNumber,Type,idPallet,Action,Description,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$asset['ServerID'])."','".mysqli_real_escape_string($this->connectionlink,$asset['ServerSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$asset['Type'])."','".mysqli_real_escape_string($this->connectionlink,$asset['idPallet'])."','".mysqli_real_escape_string($this->connectionlink,$desc)."','',NOW(),'".$_SESSION['user']['UserId']."')";
			$q3 = mysqli_query($this->connectionlink,$query3);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			//End Inserting into Asset Tracking

            $json['Success'] = true;
            $json['Result'] = $asset['Type'].' Moved to BIN';
            return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}



	public function ConsolidateServerSwitchBIN ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Server/Switch Consolidation')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Server/Switch Consolidation Page';
				return json_encode($json);
			}

            if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Server/Switch Consolidation')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Server/Switch Consolidation Page';
				return json_encode($json);
			}			
            
             //Start check CustomPallet Details
             $query = "select c.* from custompallet c where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['FromCustomPalletID'])."'";
             $q = mysqli_query($this->connectionlink,$query);
             if(mysqli_error($this->connectionlink)) {
                 $json['Success'] = false;
                 $json['Result'] = mysqli_error($this->connectionlink);
                 return json_encode($json);
             }			
             if(mysqli_affected_rows($this->connectionlink) > 0) {
                 $from_cp = mysqli_fetch_assoc($q);
                 if($from_cp['StatusID'] != '1') {
                     $json['Success'] = false;
                     $json['Result'] = 'From BIN Status is not Active';
                     return json_encode($json);
                 }
 
                 if($from_cp['FacilityID'] != $_SESSION['user']['FacilityID']) {
                     $json['Success'] = false;
                     $json['Result'] = 'From BIN Facility is different from Users Facility';
                     return json_encode($json);
                 }				
             } else {
                 $json['Success'] = false;
                 $json['Result'] = "Invalid From BIN";
                 return json_encode($json);
             }


             $query = "select c.* from custompallet c where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['ToCustomPalletID'])."'";
             $q = mysqli_query($this->connectionlink,$query);
             if(mysqli_error($this->connectionlink)) {
                 $json['Success'] = false;
                 $json['Result'] = mysqli_error($this->connectionlink);
                 return json_encode($json);
             }			
             if(mysqli_affected_rows($this->connectionlink) > 0) {
                 $to_cp = mysqli_fetch_assoc($q);
                 if($to_cp['StatusID'] != '1') {
                     $json['Success'] = false;
                     $json['Result'] = 'To BIN Status is not Active';
                     return json_encode($json);
                 }
 
                 if($to_cp['FacilityID'] != $_SESSION['user']['FacilityID']) {
                     $json['Success'] = false;
                     $json['Result'] = 'To BIN Facility is different from Users Facility';
                     return json_encode($json);
                 }				
            } else {
                 $json['Success'] = false;
                 $json['Result'] = "Invalid To BIN";
                 return json_encode($json);
            }
             //End check CustomPallet Details


            //Start check IF BIN Dispositions matches
            if($to_cp['AcceptAllDisposition'] == 0) {
                if($to_cp['disposition_id'] != $from_cp['disposition_id']) {
                    $json['Success'] = false;
					$json['Result'] = "Dispositions of the BINs are different and 'Accept All Disposition' is not enabled for To BIN";
					return json_encode($json);
                }
            }
            //End check IF BIN Dispositions matches


            //Start consolidating BINs

            //Insert into Asset Tracking
			$desc = "Moved from BIN, (BIN ID : ".$from_cp['BinName'].") to BIN, (BIN ID : ".$to_cp['BinName'].") in Server/Switch Consolidation Screen";			
            $query3 = "insert into speed_server_recovery_tracking (ServerID,ServerSerialNumber,Type,idPallet,Action,Description,CreatedDate,CreatedBy) 
            select ServerID,ServerSerialNumber,Type,idPallet,'".mysqli_real_escape_string($this->connectionlink,$desc)."','',NOW(),'".$_SESSION['user']['UserId']."' from speed_server_recovery where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$from_cp['CustomPalletID'])."' ";
			$q3 = mysqli_query($this->connectionlink,$query3);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			//End Inserting into Asset Tracking


			//Start insert into BIN movement table
			$event_id = rand(1000000000, 9999999999);
			$batch_event_flag = 'Y';
			$query23 = "insert into bin_consolidation_asset_movement (ServerID,SerialNumber,FromCustomPalletID,ToCustomPalletID,FacilityID,UniversalModelNumber,part_type,disposition_id,CreatedDate,CreatedBy,MovementType,FromBinName,ToBinName,idPallet,event_id,batch_event_flag) 
			select ServerID,ServerSerialNumber,'".$from_cp['CustomPalletID']."','".$to_cp['CustomPalletID']."','".$_SESSION['user']['FacilityID']."',MPN,Type,disposition_id,NOW(),'".$_SESSION['user']['UserId']."','BULK Aset Movement','".$from_cp['BinName']."','".$to_cp['BinName']."',idPallet,'".$event_id."','".$batch_event_flag."' from speed_server_recovery where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$from_cp['CustomPalletID'])."' 
			";
			$q23 = mysqli_query($this->connectionlink,$query23);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			//End insert into BIN movement table



            //Start update Asset
			$query1 = "update speed_server_recovery set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."' where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$from_cp['CustomPalletID'])."'";
			$q1 = mysqli_query($this->connectionlink,$query1);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}			
			//End update Asset

			//Start update Custom Pallet Items
			$query2 = "update custompallet_items set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."' where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$from_cp['CustomPalletID'])."' and ServerID > 0";
			$q2 = mysqli_query($this->connectionlink,$query2);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			//End update Custom Pallet Items

			//Start update Custom Pallet Counts
            $query33 = "select count(*) from custompallet_items where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."' ";
            $q33 = mysqli_query($this->connectionlink,$query33);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
            if(mysqli_affected_rows($this->connectionlink) > 0) {
                $row33 = mysqli_fetch_assoc($q33);
                $query3 = "UPDATE `custompallet` SET `AssetsCount`= '".mysqli_real_escape_string($this->connectionlink,$row33['count(*)'])."' WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."'";
                $q3 = mysqli_query($this->connectionlink,$query3);
                if(mysqli_error($this->connectionlink)) {
                    $json['Success'] = false;
                    $json['Result'] = mysqli_error($this->connectionlink);
                    return json_encode($json);
                }
            }			

			$query4 = "UPDATE `custompallet` SET `AssetsCount`= 0 WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$from_cp['CustomPalletID'])."'";
			$q4 = mysqli_query($this->connectionlink,$query4);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			//End update Custom Pallet Counts


            //End consolidating BINs

            $json['Success'] = true;
            $json['Result'] = 'Switch/Server Consolidation Success';
            return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}



	public function GetCustomPalletDetailsForMedia ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Media Consolidation')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Media Consolidation Page';
				return json_encode($json);
			}			
            $query = "select c.*,d.disposition from custompallet c left join disposition d on c.disposition_id = d.disposition_id where BinName = '".mysqli_real_escape_string($this->connectionlink,$data['BinName'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}			
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);
				if($row['StatusID'] != '1') {
					$json['Success'] = false;
					$json['Result'] = 'BIN Status is not Active';
					return json_encode($json);
				}

				if($row['FacilityID'] != $_SESSION['user']['FacilityID']) {
					$json['Success'] = false;
					$json['Result'] = 'BIN Facility is different from Users Facility';
					return json_encode($json);
				}

				$json['Success'] = true;
				$json['Result'] = $row;
			} else {
				$json['Success'] = false;
				$json['Result'] = "Invalid BIN";
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}



	public function AddMediaToBin ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Media Consolidation')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Media Consolidation Page';
				return json_encode($json);
			}

            if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Media Consolidation')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Media Consolidation Page';
				return json_encode($json);
			}
            
            //Start check CustomPallet Details
            $query = "select c.* from custompallet c where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}			
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$cp = mysqli_fetch_assoc($q);
				if($cp['StatusID'] != '1') {
					$json['Success'] = false;
					$json['Result'] = 'BIN Status is not Active';
					return json_encode($json);
				}

				if($cp['FacilityID'] != $_SESSION['user']['FacilityID']) {
					$json['Success'] = false;
					$json['Result'] = 'BIN Facility is different from Users Facility';
					return json_encode($json);
				}				
			} else {
				$json['Success'] = false;
				$json['Result'] = "Invalid BIN";
                return json_encode($json);
			}
            //End check CustomPallet Details

            //Start check Serial Details
            $query20 = "select * from speed_media_recovery where MediaSerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."'";
			$q20 = mysqli_query($this->connectionlink,$query20);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$asset = mysqli_fetch_assoc($q20);
				if($asset['StatusID'] == 3) {
					$json['Success'] = false;
					$json['Result'] = $asset['MediaType'].' Status is Shreded';
					return json_encode($json);
				}

				//Start get asset_cp details

				$query11 = "select c.* from custompallet c where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$asset['CustomPalletID'])."'";
				$q11 = mysqli_query($this->connectionlink,$query11);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}			
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$from_cp = mysqli_fetch_assoc($q11);									
				} else {
					$json['Success'] = false;
					$json['Result'] = "Invalid From BIN";
					return json_encode($json);
				}

				//End get asset_cp details

			} else {
				$json['Success'] = false;
				$json['Result'] = 'Invalid Serial';
				return json_encode($json);
			}
            //End check Serial Details

            //Start check IF Disposition matches
            if($cp['AcceptAllDisposition'] == 0) {//CP Accepts only specific Disposition
                if($cp['disposition_id'] != $asset['disposition_id']) {
                    $json['Success'] = false;
					$json['Result'] = $asset['MediaType'].' Disposition is not matching with BIN Disposition';
					return json_encode($json);
                }
            }
            //End check IF Disposition matches

			//Start insert into bin movement table
			$event_id = rand(1000000000, 9999999999);
			$batch_event_flag = 'N';
			$query23 = "insert into bin_consolidation_asset_movement (MediaID,SerialNumber,FromCustomPalletID,ToCustomPalletID,FacilityID,UniversalModelNumber,part_type,disposition_id,CreatedDate,CreatedBy,MovementType,FromBinName,ToBinName,idPallet,event_id,batch_event_flag) values ('".mysqli_real_escape_string($this->connectionlink,$asset['MediaID'])."','".mysqli_real_escape_string($this->connectionlink,$asset['MediaSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$asset['CustomPalletID'])."','".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."','".$_SESSION['user']['FacilityID']."','".mysqli_real_escape_string($this->connectionlink,$asset['MediaMPN'])."','".mysqli_real_escape_string($this->connectionlink,$asset['MediaType'])."','".mysqli_real_escape_string($this->connectionlink,$asset['disposition_id'])."',NOW(),'".$_SESSION['user']['UserId']."','SINGLE','".mysqli_real_escape_string($this->connectionlink,$from_cp['BinName'])."','".mysqli_real_escape_string($this->connectionlink,$cp['BinName'])."','".mysqli_real_escape_string($this->connectionlink,$asset['idPallet'])."','".$event_id."','".$batch_event_flag."')";
			$q23 = mysqli_query($this->connectionlink,$query23);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			//End insert into bin movement table

            //Start update Asset
			$query1 = "update speed_media_recovery set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."' where MediaID = '".mysqli_real_escape_string($this->connectionlink,$asset['MediaID'])."'";
			$q1 = mysqli_query($this->connectionlink,$query1);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}			
			//End update Asset

			//Start update Custom Pallet Items
			$query2 = "update custompallet_items set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."' where MediaID = '".mysqli_real_escape_string($this->connectionlink,$asset['MediaID'])."'";
			$q2 = mysqli_query($this->connectionlink,$query2);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			//End update Custom Pallet Items

			//Start update Custom Pallet Counts
			$query3 = "UPDATE `custompallet` SET `AssetsCount`= `AssetsCount` + 1 WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."'";
			$q3 = mysqli_query($this->connectionlink,$query3);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			$query4 = "UPDATE `custompallet` SET `AssetsCount`= `AssetsCount` - 1 WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$asset['CustomPalletID'])."'";
			$q4 = mysqli_query($this->connectionlink,$query4);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			//End update Custom Pallet Counts

			//Insert into Asset Tracking
			$desc = $asset['MediaType']." Moved from BIN, (BIN ID : ".$from_cp['BinName'].") to BIN, (BIN ID : ".$cp['BinName'].") in Media Consolidation Screen";
			$query3 = "insert into speed_media_recovery_tracking (MediaID,MediaSerialNumber,MediaType,idPallet,Action,Description,CreatedDate,CreatedBy,ServerSerialNumber) values ('".mysqli_real_escape_string($this->connectionlink,$asset['MediaID'])."','".mysqli_real_escape_string($this->connectionlink,$asset['MediaSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$asset['MediaType'])."','".mysqli_real_escape_string($this->connectionlink,$asset['idPallet'])."','".mysqli_real_escape_string($this->connectionlink,$desc)."','',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$asset['ServerSerialNumber'])."')";
			$q3 = mysqli_query($this->connectionlink,$query3);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			//End Inserting into Asset Tracking

            $json['Success'] = true;
            $json['Result'] = $asset['MediaType'].' Moved to BIN';
            return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}




	public function ConsolidateMediaBIN ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Media Consolidation')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Media Consolidation Page';
				return json_encode($json);
			}

            if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Media Consolidation')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Media Consolidation Page';
				return json_encode($json);
			}			
            
             //Start check CustomPallet Details
             $query = "select c.* from custompallet c where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['FromCustomPalletID'])."'";
             $q = mysqli_query($this->connectionlink,$query);
             if(mysqli_error($this->connectionlink)) {
                 $json['Success'] = false;
                 $json['Result'] = mysqli_error($this->connectionlink);
                 return json_encode($json);
             }			
             if(mysqli_affected_rows($this->connectionlink) > 0) {
                 $from_cp = mysqli_fetch_assoc($q);
                 if($from_cp['StatusID'] != '1') {
                     $json['Success'] = false;
                     $json['Result'] = 'From BIN Status is not Active';
                     return json_encode($json);
                 }
 
                 if($from_cp['FacilityID'] != $_SESSION['user']['FacilityID']) {
                     $json['Success'] = false;
                     $json['Result'] = 'From BIN Facility is different from Users Facility';
                     return json_encode($json);
                 }				
             } else {
                 $json['Success'] = false;
                 $json['Result'] = "Invalid From BIN";
                 return json_encode($json);
             }


             $query = "select c.* from custompallet c where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['ToCustomPalletID'])."'";
             $q = mysqli_query($this->connectionlink,$query);
             if(mysqli_error($this->connectionlink)) {
                 $json['Success'] = false;
                 $json['Result'] = mysqli_error($this->connectionlink);
                 return json_encode($json);
             }			
             if(mysqli_affected_rows($this->connectionlink) > 0) {
                 $to_cp = mysqli_fetch_assoc($q);
                 if($to_cp['StatusID'] != '1') {
                     $json['Success'] = false;
                     $json['Result'] = 'To BIN Status is not Active';
                     return json_encode($json);
                 }
 
                 if($to_cp['FacilityID'] != $_SESSION['user']['FacilityID']) {
                     $json['Success'] = false;
                     $json['Result'] = 'To BIN Facility is different from Users Facility';
                     return json_encode($json);
                 }				
            } else {
                 $json['Success'] = false;
                 $json['Result'] = "Invalid To BIN";
                 return json_encode($json);
            }
             //End check CustomPallet Details


            //Start check IF BIN Dispositions matches
            if($to_cp['AcceptAllDisposition'] == 0) {
                if($to_cp['disposition_id'] != $from_cp['disposition_id']) {
                    $json['Success'] = false;
					$json['Result'] = "Dispositions of the BINs are different and 'Accept All Disposition' is not enabled for To BIN";
					return json_encode($json);
                }
            }
            //End check IF BIN Dispositions matches


            //Start consolidating BINs

            //Insert into Asset Tracking
			$desc = "Moved from BIN, (BIN ID : ".$from_cp['BinName'].") to BIN, (BIN ID : ".$to_cp['BinName'].") in Media Consolidation Screen";			
            $query3 = "insert into speed_media_recovery_tracking (MediaID,MediaSerialNumber,ServerSerialNumber,MediaType,idPallet,Action,Description,CreatedDate,CreatedBy) 
            select MediaID,MediaSerialNumber,ServerSerialNumber,MediaType,idPallet,'".mysqli_real_escape_string($this->connectionlink,$desc)."','',NOW(),'".$_SESSION['user']['UserId']."' from speed_media_recovery where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$from_cp['CustomPalletID'])."' ";
			$q3 = mysqli_query($this->connectionlink,$query3);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			//End Inserting into Asset Tracking


			//Start insert into BIN movement table
			$event_id = rand(1000000000, 9999999999);
			$batch_event_flag = 'Y';
			$query23 = "insert into bin_consolidation_asset_movement (MediaID,SerialNumber,FromCustomPalletID,ToCustomPalletID,FacilityID,UniversalModelNumber,part_type,disposition_id,CreatedDate,CreatedBy,MovementType,FromBinName,ToBinName,idPallet,event_id,batch_event_flag) 
			select MediaID,MediaSerialNumber,'".$from_cp['CustomPalletID']."','".$to_cp['CustomPalletID']."','".$_SESSION['user']['FacilityID']."',MediaMPN,MediaType,disposition_id,NOW(),'".$_SESSION['user']['UserId']."','BULK Aset Movement','".$from_cp['BinName']."','".$to_cp['BinName']."',idPallet,'".$event_id."','".$batch_event_flag."' from speed_media_recovery where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$from_cp['CustomPalletID'])."' 
			";
			$q23 = mysqli_query($this->connectionlink,$query23);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			//End insert into BIN movement table



            //Start update Asset
			$query1 = "update speed_media_recovery set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."' where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$from_cp['CustomPalletID'])."'";
			$q1 = mysqli_query($this->connectionlink,$query1);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}			
			//End update Asset

			//Start update Custom Pallet Items
			$query2 = "update custompallet_items set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."' where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$from_cp['CustomPalletID'])."' and MediaID > 0";
			$q2 = mysqli_query($this->connectionlink,$query2);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			//End update Custom Pallet Items

			//Start update Custom Pallet Counts
            $query33 = "select count(*) from custompallet_items where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."' ";
            $q33 = mysqli_query($this->connectionlink,$query33);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
            if(mysqli_affected_rows($this->connectionlink) > 0) {
                $row33 = mysqli_fetch_assoc($q33);
                $query3 = "UPDATE `custompallet` SET `AssetsCount`= '".mysqli_real_escape_string($this->connectionlink,$row33['count(*)'])."' WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$to_cp['CustomPalletID'])."'";
                $q3 = mysqli_query($this->connectionlink,$query3);
                if(mysqli_error($this->connectionlink)) {
                    $json['Success'] = false;
                    $json['Result'] = mysqli_error($this->connectionlink);
                    return json_encode($json);
                }
            }			

			$query4 = "UPDATE `custompallet` SET `AssetsCount`= 0 WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$from_cp['CustomPalletID'])."'";
			$q4 = mysqli_query($this->connectionlink,$query4);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			//End update Custom Pallet Counts


            //End consolidating BINs

            $json['Success'] = true;
            $json['Result'] = 'Media Consolidation Success';
            return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function GetShipmentContainerDetails ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Outbound Container Consolidation')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Outbound Container Consolidation Page';
				return json_encode($json);
			}		
			
			$query10 = "select s.*,d.disposition from shipping_containers s 
			left join disposition d on s.disposition_id = d.disposition_id 
			where s.ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['BinName'])."'";
			$q10 = mysqli_query($this->connectionlink,$query10);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$container = mysqli_fetch_assoc($q10);
				
				if($container['StatusID'] != '1') {
					$json['Success'] = false;		
					$json['Result'] = 'Container Status is not active';			
					return json_encode($json);
				}
				if($container['FacilityID'] != $_SESSION['user']['FacilityID']) {
					$json['Success'] = false;		
					$json['Result'] = 'Container Facility is different from Users Facility';			
					return json_encode($json);
				}


				//Start get pending assets count
				$query2 = "select count(*) from shipping_container_serials where ShippingContainerID = '".$container['ShippingContainerID']."'";
				$q2 = mysqli_query($this->connectionlink,$query2);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row2 = mysqli_fetch_assoc($q2);
					$container['AssetsCount'] = $row2['count(*)'];
				} else {
					$container['AssetsCount'] = 0;
				}
				//End get pending assets count

				$json['Success'] = true;
				$json['Result'] = $container;
				return json_encode($json);
			} else {
				$json['Success'] = false;		
				$json['Result'] = 'Invalid Container';			
				return json_encode($json);
			}			            
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function ConsolidateShipmentContainer ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Outbound Container Consolidation')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Outbound Container Consolidation Page';
				return json_encode($json);
			}

            if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Outbound Container Consolidation')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Outbound Container Consolidation Page';
				return json_encode($json);
			}		
			
			//Start check From Container
			$query10 = "select s.* from shipping_containers s where s.ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['FromCustomPalletID'])."'";
			$q10 = mysqli_query($this->connectionlink,$query10);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$from_container = mysqli_fetch_assoc($q10);
				
				if($from_container['StatusID'] != '1') {
					$json['Success'] = false;		
					$json['Result'] = 'From Container Status is not active';			
					return json_encode($json);
				}
				if($from_container['FacilityID'] != $_SESSION['user']['FacilityID']) {
					$json['Success'] = false;		
					$json['Result'] = 'From Container Facility is different from Users Facility';			
					return json_encode($json);
				}				
			} else {
				$json['Success'] = false;		
				$json['Result'] = 'Invalid From Container';			
				return json_encode($json);
			}
			//End check From Container


			//Start check To Container
			$query10 = "select s.* from shipping_containers s where s.ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ToCustomPalletID'])."'";
			$q10 = mysqli_query($this->connectionlink,$query10);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$to_container = mysqli_fetch_assoc($q10);
				
				if($to_container['StatusID'] != '1') {
					$json['Success'] = false;		
					$json['Result'] = 'To Container Status is not active';			
					return json_encode($json);
				}
				if($to_container['FacilityID'] != $_SESSION['user']['FacilityID']) {
					$json['Success'] = false;		
					$json['Result'] = 'To Container Facility is different from Users Facility';			
					return json_encode($json);
				}				
			} else {
				$json['Success'] = false;		
				$json['Result'] = 'Invalid To Container';			
				return json_encode($json);
			}
			//End check Tp Container

			if($from_container['ShippingContainerID'] == $to_container['ShippingContainerID']) {
				$json['Success'] = false;		
				$json['Result'] = 'From and To Containers should be different for Consolidation';			
				return json_encode($json);
			}

			if($from_container['disposition_id'] != $to_container['disposition_id']) {
				$json['Success'] = false;		
				$json['Result'] = 'Removal Types of From and To Containers should be same for Consolidation';			
				return json_encode($json);
			}
            
             
            //Start consolidating Containers	            			
			$query1 = "update shipping_container_serials set ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$to_container['ShippingContainerID'])."' where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$from_container['ShippingContainerID'])."' ";
			$q1 = mysqli_query($this->connectionlink,$query1);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
            //End consolidating Containers			

            $json['Success'] = true;
            $json['Result'] = 'Consolidation Consolidation Success';
            return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

}
?>