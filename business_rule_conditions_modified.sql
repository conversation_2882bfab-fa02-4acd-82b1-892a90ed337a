/*
-- Query: SELECT * FROM awsstg.business_rule_condition where rule_id in (

SELECT rule_id FROM awsstg.business_rule where version_id = 71 and status = 'Active'
)
LIMIT 0, 50000

-- Date: 2025-06-09 08:46
*/
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (20,'==','Yes',6110,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (35,'contains','Extract-CPU',6111,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (21,'==','Yes',6112,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (26,'==','Yes',6113,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (6,'==','Yes',6114,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (35,'contains','Liquidation-Active',6115,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (23,'==','Critical',6117,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (11,'==','Yes',6117,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (35,'contains','Failure_Analysis-FullTest',6117,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (35,'contains','Failure_Analysis-FullTest',6118,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (12,'==','No',6118,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (1,'==','Yes',6118,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (11,'==','Yes',6119,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (12,'==','No',6119,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (1,'==','Yes',6119,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (35,'contains','Failure_Analysis-FullTest',6119,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (6,'==','No',6120,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (35,'contains','Liquidation-Active',6120,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (6,'==','No',6121,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (35,'contains','Liquidation-Active',6121,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (6,'==','No',6123,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (6,'==','No',6124,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (6,'==','Yes',6125,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (6,'==','Yes',6126,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (35,'contains','Repair-HeatSinkReplacement',6128,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (1,'==','Yes',6128,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (1,'==','Yes',6129,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (35,'contains','Sanitization-Spare',6130,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (1,'==','Yes',6130,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (6,'==','Yes',6130,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (35,'not_contains','Failure_Analysis-FullTest',6131,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (6,'==','Yes',6131,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (35,'contains','Sanitization-Recycle@#$Sanitization-Destruction@#$Sanitization-RMA@#$Sanitization-Liquidation',6131,NOW(),1075,1);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (35,'contains','Sanitization-Spare',6132,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (1,'==','Yes',6132,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (11,'==','Yes',6133,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (12,'==','No',6133,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (1,'==','Yes',6133,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (35,'contains','RMA-Standard',6134,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (35,'contains','Sanitization-RMA',6134,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (35,'contains','Liquidation-Active',6135,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (35,'contains','Sanitization-Liquidation',6135,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (35,'contains','Failure_Analysis-FullTest',6136,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (12,'==','No',6136,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (35,'contains','Repair-HeatSinkReplacement',6136,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (1,'==','Yes',6136,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (12,'==','No',6137,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (1,'==','Yes',6137,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (35,'contains','RMA-Standard',6137,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (15,'==','1024',6137,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (11,'==','Yes',6138,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (12,'==','No',6138,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (35,'contains','Failure_Analysis-FullTest',6138,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (35,'contains','Repair-HeatSinkReplacement',6138,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (1,'==','Yes',6138,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (35,'contains','Failure_Analysis-FullTest',6139,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (1,'==','Yes',6139,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (12,'==','No',6139,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (1,'==','Yes',6140,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (11,'==','Yes',6140,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (12,'==','No',6140,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (18,'==','Yes',6140,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (35,'contains','RMA-Standard',6140,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (6,'==','No',6140,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (35,'contains','Failure_Analysis-FullTest',6141,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (1,'==','Yes',6141,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (11,'==','Yes',6141,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (12,'==','No',6141,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (35,'contains','Failure_Analysis-FullTest',6142,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (1,'==','Yes',6142,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (11,'==','Yes',6142,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (35,'contains','Failure_Analysis-FullTest',6144,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (11,'==','Yes',6144,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (35,'contains','RMA-Virtual',6144,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (1,'==','Yes',6146,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (6,'==','No',6146,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (12,'==','No',6146,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (6,'==','Yes',6147,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (15,'==','1024',6147,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (1,'==','Yes',6148,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (6,'==','Yes',6148,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (35,'not_contains','Sanitization-Spare',6149,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (1,'==','Yes',6149,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (6,'==','Yes',6149,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (35,'contains','Repair-HeatSinkReplacement',6150,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (1,'==','Yes',6150,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (6,'==','No',6151,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (6,'==','No',6152,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (6,'==','No',6154,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (6,'==','No',6155,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (6,'==','No',6156,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (35,'contains','Liquidation-Active',6156,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (35,'contains','Extract-HeatSinkForRepair',6157,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (1,'==','No',6158,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (35,'contains','Extract-HeatSinkForRepair',6158,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (35,'contains','Liquidation-Delayed',6159,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (6,'==','No',6159,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (35,'contains','Failure_Analysis-FullTest',6160,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (35,'contains','Extract-HeatSinkForRepair',6160,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (1,'==','Yes',6160,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (25,'==','K2T-QB',6161,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (25,'==','K2T-QB-TP1',6162,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (25,'==','K2T-QB-1TPM',6163,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (15,'==','1636',6166,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (6,'==','No',6167,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (1,'==','Yes',6169,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (35,'contains','Repair-HeatSinkReplacement',6170,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (1,'==','Yes',6170,NOW(),1075,0);
INSERT INTO `business_rule_condition` (`attribute_id`,`operator`,`value`,`rule_id`,`created_date`,`created_by`,`multiple_values`) VALUES (26,'==','Yes',6171,NOW(),1075,0);
